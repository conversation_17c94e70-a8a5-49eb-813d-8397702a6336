# BitFunComposer Backend

This is the backend for the BitFunComposer project, an AI Coding Agent.

To run the backend server, command with **fastapi dev .\backend\main.py**.



# 设置开发环境（默认）
export COMPOSER_ENV=dev

项目使用 YAML 文件进行配置管理：
config/config.dev.yaml: 开发环境配置

# 服务部署
## conda虚拟环境
1. 安装miniconda
2. 创建虚拟环境
```bash
conda env list # 查看当前已有环境，240已创建
conda create -n composer python=3.12.11
conda activate composer
```
## 代码上传服务器
本地启动时跳过

代码上传到240机器 `/mnt/vdb/composer/BitFun_Composer-main` （如果自己部署测试改为自己的目录）
## 安装依赖
```bash
cd ${composer工程目录}
pip install -r requirements.txt
```
## composer服务启动
```bash
python backend/main.py --composer_port 8888 # 默认8888，可以不加参数
```
## 根据codebase的readme文档，启动codebase服务
## 端侧浏览器界面启动
```bash
# 连接云侧的composer
python backend/tests/end2end/router.py --composer_ip *************** --composer_port 8888
# 连接本地的composer
python backend/tests/end2end/router.py --composer_ip 127.0.0.1 --composer_port 8888
```

访问 http://127.0.0.1:5000进入端侧界面，根据提示输入项目路径和需求描述
