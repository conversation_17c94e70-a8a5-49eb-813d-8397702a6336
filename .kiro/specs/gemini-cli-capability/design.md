# 设计文档

## 概述

BitFunComposer是一个AI驱动的编程代理系统，通过复杂的大脑架构提供类似Gemini CLI的能力。系统使用FastAPI和WebSocket构建，支持实时通信，采用受心理学模型启发的模块化设计（自我、超我、本我）来实现智能任务规划和执行。

架构采用分层方法，职责清晰分离：
- **表现层**: WebSocket API和REST端点
- **大脑层**: 认知组件（Ego、Orchestrator、Planner、Hippocampus）
- **服务层**: 业务逻辑和外部集成
- **工具层**: 可扩展的代码操作工具系统
- **基础设施层**: 配置、日志和数据库管理

## 架构设计

### 高层系统架构

```mermaid
graph TB
    Client[Client Applications] --> WS[WebSocket Gateway]
    Client --> REST[REST API]
    
    WS --> SM[Session Manager]
    REST --> SM
    
    SM --> Brain[Brain System]
    
    subgraph "Brain Architecture"
        Brain --> Ego[Ego - Coordinator]
        Ego --> Planner[Planner - Task Planning]
        Ego --> Orchestrator[Orchestrator - Execution]
        Ego --> Hippocampus[Hippocampus - Memory]
        Orchestrator --> SuperEgo[SuperEgo - Validation]
    end
    
    Orchestrator --> ToolService[Tool Service]
    
    subgraph "Tool Ecosystem"
        ToolService --> ClientTools[Client Tools]
        ToolService --> BuiltinTools[Builtin Tools]
        ToolService --> MCPTools[MCP Tools]
    end
    
    ToolService --> LLMService[LLM Service]
    LLMService --> LLMProviders[LLM Providers]
    
    subgraph "Infrastructure"
        Config[Configuration]
        Logging[Logging System]
        Database[Database]
    end
```

### 大脑架构设计

大脑架构是本系统的核心创新，实现了AI推理的心理学模型：

```mermaid
graph LR
    UserQuery[用户查询] --> Ego
    
    subgraph "大脑组件"
        Ego[自我 - 执行控制]
        Id[本我 - 规划与编排]
        SuperEgo[超我 - 验证与伦理]
        Hippocampus[海马体 - 记忆管理]
    end
    
    Ego --> Id
    Id --> Planner[规划器 - 任务分解]
    Id --> Orchestrator[编排器 - 执行引擎]
    
    Planner --> Orchestrator
    Orchestrator --> Hippocampus
    Hippocampus --> Ego
    
    Orchestrator --> Tools[工具执行]
    SuperEgo --> Orchestrator
```

## 组件和接口

### 1. WebSocket通信层

**目的**: 处理与客户端的实时双向通信

**核心组件**:
- `WebSocketManager`: 管理WebSocket连接和消息路由
- `MessageParser`: 将传入消息解析为适当的请求类型
- `SessionManager`: 管理用户会话和状态隔离

**接口设计**:
```python
class WebSocketManager:
    async def handle_connection(websocket: WebSocket) -> None
    async def send_message(session_id: str, message: dict) -> None
    async def receive_message(websocket: WebSocket) -> dict
    async def close_connection(session_id: str) -> None
```

### 2. 大脑系统组件

#### Ego (执行控制器)
**目的**: 管理整体请求处理流程的中央协调器

**职责**:
- 接收用户查询并初始化处理上下文
- 协调规划器、编排器和记忆系统
- 管理整体执行生命周期

**接口**:
```python
class Ego:
    def __init__(session: Session, context: QueryContext, tool_service: ToolService)
    async def run() -> None
    def get_execution_status() -> ExecutionStatus
```

#### Planner (任务分解器)
**目的**: 将复杂的用户请求分解为可管理的执行步骤

**职责**:
- 分析用户查询并创建执行计划
- 维护计划状态和进度跟踪
- 支持基于执行结果的动态计划调整

**接口**:
```python
class Planner:
    def make_overall_plan(context: QueryContext) -> List[PlanStep]
    def get_current_step() -> Optional[PlanStep]
    def sync_progress(progress_info: dict) -> None
    def adjust_plan(feedback: ExecutionFeedback) -> None
```

#### Orchestrator (执行引擎)
**目的**: 通过工具协调执行单个计划步骤

**职责**:
- 使用可用工具执行计划步骤
- 管理工具执行上下文和结果
- 处理执行错误和重试
- 维护执行记忆以保持上下文

**接口**:
```python
class Orchestrator:
    def __init__(session: Session, context: ExecuteContext, tool_service: ToolService)
    async def get_next_action() -> Tuple[str, List[ToolCall]]
    async def execute_action(actions: List[ToolCall]) -> None
    async def run() -> None
```

#### Hippocampus (记忆管理器)
**目的**: 管理短期记忆和上下文以实现连贯对话

**职责**:
- 存储对话历史和工具执行结果
- 提供上下文感知的记忆检索
- 实现记忆压缩策略
- 加载和管理少样本示例

**接口**:
```python
class Hippocampus:
    def get_complete_memory() -> Memory
    def get_goal_related_memory(goal: str) -> Memory
    def add_tool_call_history(content: str, tool_calls: List[dict]) -> None
    def add_tool_result_history(tool_call_id: str, content: str) -> None
    def compress_history() -> None
```

### 3. 工具系统架构

**目的**: 支持多种工具类型的可扩展工具生态系统

**工具类别**:
1. **客户端工具**: 由客户端应用程序提供的工具
2. **内置工具**: 服务器端工具（代码库操作、知识查询）
3. **MCP工具**: 兼容模型上下文协议的工具

**接口设计**:
```python
class ToolService:
    def __init__(session: Session, client_tool_prompt: List[dict])
    async def execute(tool_name: str, tool_params: dict) -> ToolResult
    def get_tools_prompt(tool_names: List[str] = None) -> List[dict]
    async def initialize_mcp_servers(mcp_config: dict) -> None
```

**工具基类**:
```python
class BaseTool(ABC, BaseModel):
    name: str
    description: str
    parameters: Optional[dict]
    
    def to_param() -> dict

class BaseExecTool(BaseTool):
    async def execute(**kwargs) -> ToolResult
```

### 4. LLM服务层

**目的**: 多个LLM提供商的抽象层

**支持的提供商**:
- SiliconFlow
- VolcanoEngine
- Ollama (本地部署)

**接口设计**:
```python
class BaseLLMService(ABC):
    async def call(prompt: str, stream: bool = False) -> Tuple[str, List[dict]]
    async def call_with_context(messages: List[dict], tools: List[dict]) -> Tuple[str, List[dict]]
    async def get_embedding(text: str) -> List[float]
    async def check_service_status() -> dict
```

### 5. 会话管理

**目的**: 管理用户会话和状态隔离

**特性**:
- 唯一会话标识
- 线程安全的会话操作
- 自动会话清理
- 工具执行同步

**接口**:
```python
class SessionManager:
    def get_session(session_id: str) -> Session
    def delete_session(session_id: str) -> None
    def cleanup_inactive_sessions() -> None

class Session:
    def add_msg_to_send(message: dict) -> None
    def get_one_msg_to_send() -> Optional[dict]
    def receive_one_msg(msg_id: str, message: dict) -> None
    def fetch_received_msg(msg_id: str) -> Optional[dict]
```

## 数据模型

### 核心数据结构

```python
# 请求/响应模型
class RequestData(BaseModel):
    query: str

class Request(BaseModel):
    session: SessionData
    tools: List[dict]
    data: MsgData[RequestData]

class ToolUseRequest(BaseModel):
    session: SessionData
    data: MsgData[ToolUseData]

# 会话模型
class SessionData(BaseModel):
    device_id: str
    session_id: str
    interaction_id: str

# 记忆模型
class Memory(BaseModel):
    init_memory: List[dict]
    new_memory: List[dict]
    
    def get_memory() -> List[dict]
    def add_tool_call_history(content: str, tool_calls: List[dict]) -> None
    def add_tool_result_history(tool_call_id: str, content: str) -> None

# 工具模型
class ToolResult(BaseModel):
    content: Any = None
    error: Optional[str] = None

class LLMToolCall(BaseModel):
    id: str
    type: str
    function: dict
```

### 配置模型

```python
# 工具配置
class ToolConfig(BaseModel):
    tool_triggers: dict
    execution_strategies: dict

# LLM配置
class LLMConfig(BaseModel):
    provider: str
    api_endpoint: str
    api_key: str
    model_name: str
    request_timeout: float
    max_retries: int
```

## 错误处理

### 错误类别和策略

1. **网络错误**
   - 连接超时
   - 服务不可用
   - 速率限制
   - **策略**: 指数退避重试与熔断器

2. **工具执行错误**
   - 无效参数
   - 工具未找到
   - 执行超时
   - **策略**: 优雅降级与替代工具

3. **LLM服务错误**
   - API配额超限
   - 模型不可用
   - 响应解析错误
   - **策略**: 提供商回退和错误上下文保持

4. **会话管理错误**
   - 会话未找到
   - 并发访问冲突
   - 内存溢出
   - **策略**: 会话恢复和资源清理

### 错误处理实现

```python
class ErrorHandler:
    @staticmethod
    async def handle_tool_error(error: Exception, context: dict) -> ToolResult:
        # 记录带上下文的错误
        # 尝试恢复策略
        # 返回优雅失败响应
        
    @staticmethod
    async def handle_llm_error(error: Exception, retry_count: int) -> bool:
        # 判断是否适合重试
        # 如需要切换到回退提供商
        # 返回重试决策
        
    @staticmethod
    def handle_session_error(error: Exception, session_id: str) -> None:
        # 清理损坏的会话状态
        # 通知客户端会话重置
        # 记录用于监控
```

## 测试策略

### 测试金字塔

1. **单元测试**
   - 单个组件测试
   - 模拟外部依赖
   - 测试覆盖率: >80%

2. **集成测试**
   - 组件交互测试
   - 数据库集成
   - 外部服务集成

3. **端到端测试**
   - 完整工作流测试
   - WebSocket通信
   - 多会话场景

### 测试类别

```python
# 单元测试示例
class TestEgo:
    async def test_query_processing()
    async def test_error_handling()
    
class TestOrchestrator:
    async def test_tool_execution()
    async def test_context_management()

class TestHippocampus:
    def test_memory_management()
    def test_context_retrieval()

# 集成测试示例
class TestToolService:
    async def test_client_tool_execution()
    async def test_builtin_tool_execution()
    async def test_mcp_tool_integration()

# 端到端测试示例
class TestWorkflow:
    async def test_complete_code_generation_flow()
    async def test_multi_step_task_execution()
    async def test_session_management()
```

### 测试基础设施

- **测试夹具**: 可重用的测试数据和模拟对象
- **测试工具**: 常用测试操作的辅助函数
- **模拟服务**: 模拟外部依赖
- **性能测试**: 负载测试和基准测试

## 安全考虑

### 认证和授权
- 基于会话的认证
- 外部服务的API密钥管理
- 每会话速率限制

### 数据保护
- 输入验证和清理
- 安全配置管理
- 敏感数据加密

### 工具安全
- 工具执行沙箱
- 参数验证
- 资源使用限制

## 性能优化

### 缓存策略
- LLM响应缓存
- 工具结果缓存
- 配置缓存

### 资源管理
- 连接池
- 内存使用优化
- 异步操作优化

### 监控和指标
- 响应时间跟踪
- 错误率监控
- 资源利用率指标

## 部署架构

### 容器策略
```dockerfile
# 多阶段构建优化
FROM python:3.11-slim as base
# 依赖和应用程序设置
```

### 配置管理
- 环境特定配置
- 密钥管理
- 功能标志

### 可扩展性考虑
- 水平扩展支持
- 负载均衡策略
- 数据库连接管理