# 场景分类框架设计讨论文档

## 讨论背景

在分析鸿蒙应用逻辑代码增量生成的场景时，我们需要建立一个既能完整覆盖用户使用场景，又便于客观评测的分类框架。本文档记录了框架设计的思考过程和验证分析。

## 分类框架演进过程

### 初始考虑的分类方式

#### 1. 按任务复杂度分类
- 简单功能增量（单组件修改）
- 中等复杂功能（多组件协调）
- 复杂业务逻辑（跨模块集成）

**放弃原因**：
- 主观性太强，难以客观界定复杂度边界
- 同一功能对不同开发者复杂度认知不同
- 无法为评测提供客观标准

#### 2. 按开发阶段分类
- 需求理解阶段
- 方案设计阶段
- 代码实现阶段
- 验证确认阶段

**放弃原因**：
- 割裂了用户的完整体验流程
- 每个场景都涉及多个阶段，导致分析重复
- 阶段能力和场景完成能力是不同的评测维度

### 最终采用的分类框架

#### 三分类体系
1. **主流程场景** - 正常的开发流程路径
直接代码生成：最基础的需求→代码路径
设计驱动开发：需求→设计→代码的完整路径
上下文指定开发：用户主动控制上下文的精准路径
2. **迭代场景** - 基于已有结果的修改和优化
需求变更重新生成：业务需求演进
设计修复迭代：设计层面的优化
代码局部重构：代码层面的精修
3. **异常场景** - 错误处理和恢复流程
编译错误修复：技术层面的问题处理
用户主动终止：用户控制权的体现
系统异常恢复：系统鲁棒性的体现

## 分类框架优势验证

### 优势1：用户视角一致性

**理论描述**：每个分类都对应用户的真实使用模式

**具体例证**：

**场景实例**：开发者要在现有的用户中心页面添加积分查询功能

- **主流程场景视角**：用户关心的是"我描述需求，系统帮我实现功能"这个完整流程
- **如果按阶段分类**：用户需要分别关注"需求理解是否准确"、"设计是否合理"、"代码是否正确"等割裂的评价点
- **如果按复杂度分类**：用户很难判断这个需求属于"简单"还是"中等复杂"

**验证结果**：三分类体系让用户能够直观理解每个场景的含义和价值。

### 优势2：场景完整性覆盖

**理论描述**：覆盖了从初次使用到深度使用的全流程

**具体例证**：

**真实开发流程追踪**：
1. **第一次使用**：开发者描述需求"添加积分查询功能" → 主流程场景
2. **需求调整**：产品经理说"积分显示改为卡片样式" → 迭代场景（需求变更）
3. **代码问题**：发现生成的网络请求有bug → 迭代场景（代码局部重构）
4. **编译错误**：代码合入后编译失败 → 异常场景（编译错误修复）
5. **意外中断**：开发者临时有事需要停止操作 → 异常场景（用户主动终止）

**对比其他分类方式**：
- **按复杂度分类**：无法处理同一功能在不同阶段的复杂度变化
- **按阶段分类**：无法体现迭代开发的连续性和上下文继承

**验证结果**：三分类体系完整覆盖了开发者的真实使用路径。

### 优势3：评测友好性

**理论描述**：每个场景都有明确的成功标准和评测点

**具体例证**：

**评测实例对比**：

**主流程场景评测**：
- 输入：自然语言需求描述
- 输出：可编译运行的功能代码
- 评测标准：功能正确性、代码质量、集成无缝性
- 评测方法：端到端的功能验证

**如果按阶段分类的评测困难**：
- "需求理解阶段"：如何单独评测需求理解？需求理解的好坏只能通过最终代码质量体现
- "代码实现阶段"：如何脱离需求理解和方案设计单独评测代码实现？

**迭代场景评测**：
- 输入：已有代码 + 修改要求
- 输出：修改后的代码
- 评测标准：修改精准性、影响最小化、功能完整性
- 评测方法：对比修改前后的差异和功能变化

**验证结果**：三分类体系为每个场景提供了清晰的评测边界和标准。

### 优势4：逻辑清晰性

**理论描述**：场景间的关系和流转规则明确

**具体例证**：

**场景流转实例**：

```
用户请求："在用户中心添加积分查询"
↓
主流程场景：直接代码生成
↓
用户反馈："积分显示样式不对"
↓
迭代场景：代码局部重构
↓
系统检测：编译错误
↓
异常场景：编译错误修复
↓
回到迭代场景：确认修复结果
↓
完成
```

**流转规则验证**：
1. **主流程 → 迭代**：用户对结果不满意，需要调整
2. **任何场景 → 异常**：出现错误需要处理
3. **异常 → 原场景**：错误处理完成后继续原流程

**对比其他分类的流转混乱**：
- 按复杂度分类：简单功能可能变复杂，复杂功能可能简化，流转逻辑不清
- 按阶段分类：阶段间可能需要回退，但回退逻辑复杂且不直观

**验证结果**：三分类体系的流转逻辑简单清晰，符合用户的心理模型。

## 主流程场景的增量开发特性

### 重要澄清

**主流程场景不仅适用于从零开发，同样适用于在已有代码基础上新增功能。**

### 具体验证

**场景实例1：从零开发**
- 用户：在空白项目中创建用户中心页面
- 系统行为：分析项目结构，生成完整的页面组件
- 特点：需要创建新文件，建立新的代码结构

**场景实例2：已有代码基础上新增功能**
- 用户：在现有用户中心页面添加积分查询功能
- 系统行为：分析现有页面结构，在合适位置插入新功能代码
- 特点：需要理解现有代码，进行增量修改

**共同特征**：
1. 都是用户描述需求，系统生成代码的完整流程
2. 都需要需求理解、代码生成、验证确认的完整步骤
3. 都以功能实现为最终目标

**差异处理**：
- 系统在处理时会自动识别是否有现有代码基础
- 代码生成策略会根据现有代码情况自动调整
- 但从用户体验角度，流程是一致的

### 与迭代场景的区别

**主流程场景**：用户提出新的功能需求
- "我要添加积分查询功能"
- "我要实现用户登录"
- "我要添加商品收藏功能"

**迭代场景**：用户对已实现的功能提出修改
- "把积分查询的样式改成卡片"
- "登录失败的提示信息改一下"
- "收藏按钮的位置调整一下"

**关键区别**：主流程是功能的首次实现，迭代是对已实现功能的调整。

## 框架对增量开发场景的完整覆盖分析

### 增量开发的核心特征

1. **渐进式功能添加** - 在现有代码基础上逐步添加新功能
2. **迭代式优化改进** - 对已有功能进行持续优化
3. **错误修正和恢复** - 处理开发过程中的各种问题
4. **用户主导的开发节奏** - 用户控制开发的进度和方向

### 框架覆盖验证

#### ✅ 渐进式功能添加
- **主流程场景**完全覆盖：无论是从零开始还是在已有代码基础上，添加新功能都属于主流程场景

#### ✅ 迭代式优化改进
- **迭代场景**专门处理：需求变更、设计修复、代码重构都有对应的子场景

#### ✅ 错误修正和恢复
- **异常场景**全面覆盖：编译错误、系统异常、用户中断都有处理机制

#### ✅ 用户主导的开发节奏
- **所有场景**都强调用户确认和控制权，支持用户在任何时候暂停、修改、重新开始

### 可能的扩展场景

经过分析，当前框架已经很完整，但可以考虑在具体场景中补充：

1. **功能依赖管理**：新功能依赖已有功能时的处理（可归入主流程场景的复杂情况）
2. **多功能并行开发**：同时开发多个独立功能（可通过多个主流程场景并行处理）
3. **版本管理集成**：与Git等工具的集成（属于系统能力，不是用户场景）

**结论**：当前的三分类框架已经能够完整覆盖增量开发的核心场景。

## 框架最终确认

### 分类框架
1. **主流程场景**：正常的开发流程路径（包括从零开发和在已有代码基础上新增功能）
2. **迭代场景**：基于已有结果的修改和优化
3. **异常场景**：错误处理和恢复流程

### 框架优势
1. **用户视角一致性**：符合用户的真实使用模式和心理模型
2. **场景完整性覆盖**：覆盖从初次使用到深度使用的全流程
3. **评测友好性**：每个场景都有明确的评测边界和标准
4. **逻辑清晰性**：场景间的关系和流转规则简单明确

### 应用价值
- 为系统设计提供清晰的功能边界
- 为产品评测提供客观的评价标准
- 为竞品对比提供统一的分析框架
- 为用户体验优化提供明确的改进方向

这个框架经过充分的理论分析和实例验证，能够有效支撑鸿蒙应用逻辑代码增量生成场景的全面分析和评测。