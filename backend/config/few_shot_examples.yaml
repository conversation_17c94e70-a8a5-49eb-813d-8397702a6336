tool_call_examples:
  - role: "assistant"
    content: "检查自身服务是否运行正常。"
    tool_calls:
      - function:
          arguments: "{}"
          name: "check_self_status"
        id: "call_8p0opi8ghdy4bpwktfzohian"
        type: "function"
  - role: "tool"
    content: "运行正常。"
    tool_call_id: "call_8p0opi8ghdy4bpwktfzohian"

# 可以根据不同场景配置不同的示例
scenarios:
  default:
    - role: "assistant"
      content: "检查自身服务是否运行正常。"
      tool_calls:
        - function:
            arguments: "{}"
            name: "check_self_status"
          id: "call_8p0opi8ghdy4bpwktfzohian"
          type: "function"
    - role: "tool"
      content: "运行正常。"
      tool_call_id: "call_8p0opi8ghdy4bpwktfzohian"
  
  codebase_search:
    - role: "assistant"
      content: "搜索代码库中的相关信息。"
      tool_calls:
        - function:
            arguments: '{"query": "示例查询"}'
            name: "search_codebase"
          id: "call_example_search"
          type: "function"
    - role: "tool"
      content: "找到相关代码片段。"
      tool_call_id: "call_example_search"