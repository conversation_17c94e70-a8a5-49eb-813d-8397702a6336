# 工具触发器配置
tool_triggers:
  # 需要刷新代码库索引的工具
  codebase_update_triggers:
    - "edit_file"
    - "write_to_file"
    - "delete_file"
    - "create_file"
  
  # 需要在执行前刷新索引的工具
  codebase_refresh_triggers:
    - "search_codebase"
  
  # 文件操作相关工具
  file_operation_tools:
    - "edit_file"
    - "write_to_file"
    - "read_file"
    - "delete_file"
    - "create_file"
  
  # 代码库操作工具
  codebase_tools:
    - "search_codebase"
    - "analyze_codebase"
    - "get_file_structure"

# 工具执行策略
execution_strategies:
  # 是否自动刷新代码库索引
  auto_refresh_codebase: true
  
  # 执行失败时的重试次数
  max_retries: 3
  
  # 工具执行超时时间（秒）
  timeout: 30