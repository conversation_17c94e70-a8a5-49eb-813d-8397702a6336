"""
端云交互的Session管理
"""
import hashlib
import threading
import time

from backend.src.brain.hippocampus.hippocampus import Hippocampus
from backend.src.core.logging import get_module_logger, trace_var
from backend.src.schemas.request_schema import ComposerResponse
from backend.src.schemas.session_data_schema import SessionData
from backend.src.evaluation.metrics import Recorder
from backend.src.utils.parser import Parser

logger = get_module_logger(__name__)


class Session:
    def __init__(self, session_id):
        self.session_id: SessionData = session_id

        # TODO 信息缓存池单开一个类
        self.send_message_cache = []
        self.send_message_cache_lock = threading.Lock()
        self.received_message_cache = {}
        self.received_message_cache_lock = threading.Lock()
        self.tool_call_cond_lock = threading.Lock()
        self.tool_call_cond: dict[str, threading.Condition] = {}

        self.hippocampus = Hippocampus()
        self.executor = None
        self.is_running = True
        self.user_query = ""
        self.supplement_info = ""
        self.recorder = Recorder()

    def add_msg_to_send(self, msg):
        with self.send_message_cache_lock:
            self.send_message_cache.append(msg)

    def add_response_msg_to_send(self, msg):
        if not msg:
            return
        response = ComposerResponse.construct_from_param(self.session_id, "", msg)
        with self.send_message_cache_lock:
            self.send_message_cache.append(response.model_dump())

    def check_msg_to_send(self):
        with self.send_message_cache_lock:
            return len(self.send_message_cache) > 0

    def get_one_msg_to_send(self):
        with self.received_message_cache_lock:
            if len(self.send_message_cache) == 0:
                return None
            return self.send_message_cache.pop(0)

    def receive_one_msg(self, msg_id, msg):
        trace_var.set(str(self.session_id))
        with self.received_message_cache_lock:
            logger.debug(f"receive one message {msg_id=}: {msg=}")
            self.received_message_cache.update({msg_id: msg})
            with self.tool_call_cond_lock:
                if msg_id in self.tool_call_cond:
                    with self.tool_call_cond[msg_id]:
                        self.tool_call_cond[msg_id].notify()

    def fetch_received_msg(self, msg_id):
        with self.received_message_cache_lock:
            return self.received_message_cache.pop(msg_id, {})

    def stop(self):
        logger.debug("session stop")
        self.is_running = False
        self.recorder.set_end_time(time.time())
        self.hippocampus.original_memory.export_to_file(f"{self.get_session_name()}.json")
        self.recorder.save_to_csv(f"{self.get_session_name()}.csv")

    def go_on(self):
        logger.debug("session go on")
        self.is_running = True

    def add_user_query(self, query):
        self.user_query = query

    def get_running_state(self):
        return self.is_running

    def get_session_name(self):
        query_hash_hex = hashlib.md5(self.user_query.encode("utf-8")).hexdigest()[:6]
        # deviceid_md5(query)_YYMMDD_HHMMSS
        time_str = Parser.parse_timestamp_to_str(int(self.session_id.interaction_id)/1000, '%Y%m%d_%H%M%S')
        return "_".join([self.session_id.device_id, query_hash_hex, time_str])

    def set_supplement_info(self, info):
        self.supplement_info = info

