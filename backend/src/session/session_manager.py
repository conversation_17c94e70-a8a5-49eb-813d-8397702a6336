import threading

from backend.src.session.session import Session


class SessionManager:
    def __init__(self):
        self.sessions = {}
        self.session_lock = threading.Lock()

    def get_session(self, session_id):
        """get session by connection_id"""
        with self.session_lock:
            if session_id not in self.sessions:
                self.sessions[session_id] = Session(session_id)
            return self.sessions[session_id]

    def delete_session(self, connection_id):
        if connection_id:
            with self.session_lock:
                self.sessions.pop(connection_id, None)
