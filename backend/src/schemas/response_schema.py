from typing import Optional

from pydantic import BaseModel, field_validator

from backend.src.schemas.session_data_schema import SessionData
from backend.src.schemas.msg_data_schema import MsgData
from backend.src.tools import ToolResult
from backend.src.utils.parser import Parser


class ToolUseData(BaseModel):
    toolId: str
    params: dict


"""{
    "session": {
        "device_id": "123",
        "session_id": "456",
        "interaction_id": "789"
    },
    "data": {
        "msg_type": "tool_response",
        "msg_id": "msg_id",
        "msg": tool_result
    }
}"""


class ToolUseResponse(BaseModel):
    session: SessionData
    data: MsgData[ToolResult]

    @staticmethod
    def construct_from_json(json_input):
        return ToolUseResponse(**json_input)


class LogData(BaseModel):
    msg: str


"""{
    "session": {
        "device_id": "123",
        "session_id": "456",
        "interaction_id": "789"
    },
    "data": {
        "msg_type": "log",
        "msg_id": "msg_id",
        "msg": "msg"
    }
}"""


class LogResponse(BaseModel):
    session: SessionData
    data: MsgData[LogData]

    @staticmethod
    def construct_from_param(session_id, msg, msg_id):
        return LogResponse(session=session_id,
                           data=MsgData[LogData](msg_type="log", msg_id=msg_id,
                                                 msg=LogData(msg=msg)))


class LLMToolCallFunction(BaseModel):
    name: str
    arguments: dict

    @field_validator('arguments', mode='before')
    @classmethod
    def parse_json_str(cls, v):
        if isinstance(v, str):
            return Parser.parse_json_from_str(v)
        return v


class LLMToolCall(BaseModel):
    id: str
    type: str = "function"
    function: LLMToolCallFunction


class StopResponse(BaseModel):
    session: SessionData
    data: MsgData[Optional[str]]

    @staticmethod
    def construct_from_param(session_id):
        return StopResponse(session=session_id, data=MsgData(msg_type="stop", msg_id=None, msg=None))


if __name__ == "__main__":
    test_json = {
      'session': {
        'device_id': '1',
        'session_id': '2',
        'interaction_id': '1751532214566.993'
      },
      'data': {
        'msg_type': 'tool_response',
        'msg_id': 'xxx',
        'msg': {
          'content': None,
          'error': 'File C:/Users/<USER>/Desktop/projects/test/test.json already exists'
        }
      }
    }


    # data = MsgData[ToolResult](msg_type=json_input.get("data").get("msg_type"),
    #                            msg_id=json_input.get("data").get("msg_id"), msg=ToolResult(content=json_input.get("data").get("msg").get("content"), error=json_input.get("data").get("msg").get("error")))
    print(ToolUseResponse(**test_json))


