from typing import List

from pydantic import BaseModel

from backend.src.schemas.msg_data_schema import MsgData
from backend.src.schemas.session_data_schema import SessionData


class RequestData(BaseModel):
    query: str


class ComposerData(BaseModel):
    msg: str


"""{
    "session": {
        "device_id": "123",
        "session_id": "456",
        "interaction_id": "789"
    },
    "tools": [{xxx}]
    "data": {
        "msg_type": "query",
        "msg_id": "msg_id",
        "msg": "msg"
    }
}"""


class Request(BaseModel):
    session: SessionData
    tools: List[dict]
    data: MsgData[RequestData]

    @staticmethod
    def construct_from_json(json_data):
        return Request(
            session=SessionData.construct_form_message(json_data),
            tools=json_data.get("tools"),
            data=json_data.get("data")
        )

class SupplementRequest(BaseModel):
    session: SessionData
    data: MsgData[RequestData]

    @staticmethod
    def construct_from_json(json_data):
        return SupplementRequest(
            session=SessionData.construct_form_message(json_data),
            data=json_data.get("data"))

class ToolUseData(BaseModel):
    tool_name: str
    params: dict


"""{
    "session": {
        "device_id": "123",
        "session_id": "456",
        "interaction_id": "789"
    },
    "data": {
        "msg_type": "tool_use",
        "msg_id": msg_id,
        "msg":{
            "tool_name": tool_name,
            "params": {"p1":"11", "p2":"22"}
        }
    }
}"""


class ToolUseRequest(BaseModel):
    session: SessionData
    data: MsgData[ToolUseData]

    @staticmethod
    def construct_from_param(session_data, msg_id, tool_name, params):
        return ToolUseRequest(session=session_data,
                              data=MsgData[ToolUseData](msg_type="tool_use", msg_id=msg_id,
                                                        msg=ToolUseData(tool_name=tool_name, params=params)))


class ComposerResponse(BaseModel):
    session: SessionData
    data: MsgData[ComposerData]

    @staticmethod
    def construct_from_param(session_data, msg_id, msg):
        return ComposerResponse(session=session_data,
                                data=MsgData[ComposerData](msg_type="response",
                                                           msg_id=msg_id,
                                                           msg=ComposerData(msg=msg)))


if __name__ == "__main__":
    request = Request.construct_from_json(
        {
            "session": {
                "device_id": "123",
                "session_id": "456",
                "interaction_id": "789"
            },
            "data": {
                "msg_type": "query_agent",
                "msg_id": "123",
                "msg": {
                    "query": "请帮我看看代码写得好不好。代码目录在D:/workspace/project"
                }
            },
            "tools": [
                {
                    'type': 'function',
                    'function': {
                        'name': 'list_dir',
                        'description': '查看目录.',
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'directory': {
                                    'type': 'string',
                                    'description': '绝对路径.',
                                },
                            },
                            'required': ['directory'],
                        },
                    }
                }, {
                    'type': 'function',
                    'function': {
                        'name': 'view_file',
                        'description': '查看文件.',
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'file_path': {
                                    'type': 'string',
                                    'description': '文件地址.',
                                },
                            },
                            'required': ['file_path'],
                        },
                    }
                }
            ]
        }
    )
    print(request)
