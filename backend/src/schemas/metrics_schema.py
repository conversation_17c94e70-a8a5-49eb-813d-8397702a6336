from pydantic import BaseModel
from enum import Enum


class StepType(str, Enum):
    USER_REQUEST = "UserRequest"
    PLANNING = "Planning"
    EXECUTION = "Tool"
    SUMMARY = "Summary"
    USER_FEEDBACK = "UserFeedback"
    OTHER = "other"


class TimeUsage(BaseModel):
    start_time: float
    end_time: float
    latency: float


class TokenUsage(BaseModel):
    model: str = ""
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0


class ProcessData(BaseModel):
    trace_id: str
    step: int = 0
    step_type: StepType
    step_input: dict | str | None = None
    step_output: dict | str | None = None
    time_usage: TimeUsage | None = None
    token_usage: TokenUsage | None = None
