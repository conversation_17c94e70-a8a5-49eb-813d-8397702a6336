from pydantic import BaseModel

"""
"session": {
    "device_id": "123",
    "session_id": "456",
    "interaction_id": "789"
}
"""
class SessionData(BaseModel):
    device_id: str
    session_id: str
    interaction_id: str

    @staticmethod
    def construct_form_message(message):
        device_id = message.get("session", {}).get("device_id", "")
        session_id = message.get("session", {}).get("session_id", "")
        interaction_id = message.get("session", {}).get("interaction_id", "")
        return SessionData(device_id=device_id, session_id=session_id, interaction_id=interaction_id)

    @staticmethod
    def construct_form_request(request):
        device_id = request.session.device_id
        session_id = request.session.session_id
        interaction_id = request.session.interaction_id
        return SessionData(device_id=device_id, session_id=session_id, interaction_id=interaction_id)

    def __hash__(self):
        return hash(self.__str__())

    def __eq__(self, other):
        return ("_".join([self.device_id, self.session_id, self.interaction_id]) ==
                "_".join([other.device_id, other.session_id, other.interaction_id]))

    def __str__(self):
        return "_".join([self.device_id, self.interaction_id, self.session_id])
