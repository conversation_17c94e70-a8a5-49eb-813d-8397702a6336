""""""
import asyncio
import json
import re
from datetime import datetime

import pytz

from backend.src.core.logging import get_module_logger
from backend.src.services.llm_service import LLMServiceFactory

logger = get_module_logger(__name__)


class Parser:
    @staticmethod
    def fix_json_with_llm(bad_json, err_msg):
        system_prompt = """你是一个json格式修复专家，识别json格式问题并进行修复。"""
        prompt = f"""请分析json解析失败原因，并输出修复后json。      
        # 输出示例
        ```json
        [
        {{
            "name": "工具调用1",
            "score": 3,
            "problem": "问题1：XXXX；问题2：XXXX",
            "advice": "问题1：XXXX；问题2：XXXX",
        }},
        {{
            "name": "工具调用2",
            "score": 4,
            "score_reason": "优点XXXX。缺点XXX。总体上XXXX。",
            "problem": "问题1：XXXX；问题2：XXXX",
            "advice": "问题1：XXXX；问题2：XXXX",
        }}
        ]
        ```
        # 有问题的json
        {bad_json}
        
        # 解析json时的报错
        {err_msg}
        """
        messages = [
            {
                "role": "system",
                "content": system_prompt
            }, {
                "role": "user",
                "content": prompt
            }
        ]
        logger.debug(f"message is {messages}")
        llm = LLMServiceFactory.create_service()

        content, _, _ = asyncio.run(llm.call_with_context(messages=messages))
        logger.debug(f"fix_json_with_llm content is {content}")

        return Parser.parse_json_from_noisy_str(content)

    @staticmethod
    def parse_json_from_str(origin_input):
        """"""
        try:
            parsed_json = json.loads(origin_input)
            while isinstance(parsed_json, str):
                parsed_json = json.loads(origin_input)
            return parsed_json
        except json.decoder.JSONDecodeError as e:
            logger.info("try repair with llm")
            return Parser.fix_json_with_llm(origin_input, e)

    @staticmethod
    def parse_json_from_noisy_str(origin_input):
        extracted_jsons = re.findall("```json\s+(.*?)\s*```", origin_input, re.DOTALL)
        if len(extracted_jsons) > 0:
            extracted_json = extracted_jsons[-1]
            return Parser.parse_json_from_str(extracted_json)
        logger.error(f"no json found: {origin_input}")
        return None

    @staticmethod
    def exclude_fields_from_json(data, excluded_fields):
        """
        从JSON数据中排除特定字段

        参数:
            data: 原始JSON数据（可以是Python字典或JSON字符串）
            excluded_fields: 要排除的字段列表

        返回:
            新的JSON对象（字典），不包含指定字段
        """
        # 如果输入是JSON字符串，先转换为Python字典
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                raise ValueError("输入的字符串不是有效的JSON格式")

        # 如果不是字典类型，直接返回（或可以根据需求处理）
        if not isinstance(data, dict):
            return data

        # 递归处理嵌套结构
        def process_item(item):
            if isinstance(item, dict):
                return {k: process_item(v) for k, v in item.items()
                        if k not in excluded_fields}
            elif isinstance(item, list):
                return [process_item(i) for i in item]
            else:
                return item

        result = process_item(data)
        return result

    @staticmethod
    def parse_timestamp_to_str(timestamp, time_format ="%Y-%m-%d %H:%M:%S"):
        beijing_tz = pytz.timezone('Asia/Shanghai')
        return datetime.fromtimestamp(timestamp, tz=beijing_tz).strftime(time_format)

if __name__ == '__main__':
    logger.info(Parser.exclude_fields_from_json({
        "from_task_id":0,
        "id":0,"name":"定位打卡模块文件位置",
        "description":"确定打卡模块相关的UI布局文件和逻辑代码文件位置",
        "details":"1. 使用目录读取能力遍历工程目录C:UsersgaosongDesktopprojectstestoh-exa"
    }, ["from_task_id"]))

    test_input = """```json
[
    {
        "name": "用户友好性",
        "score": 7,
        "score_reason": "优点：1. 在关键节点（创建UserPreferences、LoginForm、UserProfile、修改Index.ets）都向用户说明了当前步骤和下一步计划，符合“礼仪”要求；2. 使用任务拆分（add_sub_task / set_task_status）让用户可以跟踪进度；3. 在修改核心文件Index.ets前，先查看原文件结构并告知用户“现在更新'我的'页面...”，体现了关键节点告知。缺点：1. 没有在任何步骤主动停下来“询问”用户是否同意继续，而是直接执行；2. 没有给出交互式调试入口或让用户选择实现方案；3. 在创建icon_arrow_right.xml时未确认用户是否已有替代图标，可能造成冗余。",
        "problem": "距离10分差距：缺少真正的交互式决策点确认；未让用户在多种实现方案中选择；未提供可视化调试或预览手段。",
        "advice": "1. 在创建LoginForm和UserProfile前，先向用户展示两种UI风格（极简 vs 丰富）的示意图或文字描述，让用户选择；2. 在修改Index.ets前，弹出“我将把现有第5个tab内容替换为登录/个人信息切换，是否继续？”的确认；3. 提供实时预览命令或截图，让用户在每一步都能看到效果并决定是否需要调整；4. 在新增资源文件前询问用户是否已存在对应图标，避免覆盖。"
    }
]
```"""
    logger.info(Parser.parse_json_from_noisy_str(test_input))
    extracted_jsons = re.findall(r"```json\s+(.*?)\s*```", test_input, re.DOTALL)
    logger.info(extracted_jsons[0])  # 看看提取的内容是否正确
