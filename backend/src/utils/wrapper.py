import time
from backend.src.core.logging import app_logger as logger
from backend.src.schemas.metrics_schema import TimeUsage

def time_mark(func):
    """get time consumption of a function"""
    def inner(*args, **kwargs):
        time_start = time.time()
        res = func(*args, **kwargs)
        time_end = time.time()
        latency = time_end - time_start
        time_usage = TimeUsage(latency=latency, start_time=time_start, end_time=time_end)
        inner.time_usage = time_usage
        logger.info(f"function {func.__name__} took {latency} seconds")
        return res
    return inner

def async_time_mark(func):
    """get time consumption of a function"""
    async def inner(*args, **kwargs):
        time_start = time.time()
        res = await func(*args, **kwargs)
        time_end = time.time()
        latency = time_end - time_start
        time_usage = TimeUsage(latency=latency, start_time=time_start, end_time=time_end)
        inner.time_usage = time_usage
        logger.info(f"function {func.__name__} took {latency} seconds")
        return res
    return inner
