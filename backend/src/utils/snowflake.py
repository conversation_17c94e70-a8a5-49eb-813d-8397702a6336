import time
import threading

from backend.src.core.config import settings

class Snowflake:
    def __init__(self):
        """
        初始化
        :param worker_id: 工作机器ID (0-31)
        :param datacenter_id: 数据中心ID (0-31)
        """
        worker_id = settings.WORKER_ID
        datacenter_id = settings.DATA_CENTER_ID
        # 起始的时间戳 (2020-01-01)
        self.twepoch = 1577836800000
        
        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.sequence = 0
        
        # 位数分配
        self.worker_id_bits = 5
        self.datacenter_id_bits = 5
        self.max_worker_id = -1 ^ (-1 << self.worker_id_bits)
        self.max_datacenter_id = -1 ^ (-1 << self.datacenter_id_bits)
        self.sequence_bits = 12
        
        # 移位偏移量
        self.worker_id_shift = self.sequence_bits
        self.datacenter_id_shift = self.sequence_bits + self.worker_id_bits
        self.timestamp_left_shift = self.sequence_bits + self.worker_id_bits + self.datacenter_id_bits
        self.sequence_mask = -1 ^ (-1 << self.sequence_bits)
        
        self.last_timestamp = -1
        
        self.lock = threading.Lock()
        
        # 参数校验
        if self.worker_id > self.max_worker_id or self.worker_id < 0:
            raise ValueError(f"worker ID can't be greater than {self.max_worker_id} or less than 0")
        if self.datacenter_id > self.max_datacenter_id or self.datacenter_id < 0:
            raise ValueError(f"datacenter ID can't be greater than {self.max_datacenter_id} or less than 0")
    
    def _gen_timestamp(self):
        """生成当前时间戳(毫秒)"""
        return int(time.time() * 1000)
    
    def _til_next_millis(self, last_timestamp):
        """等待下一毫秒"""
        timestamp = self._gen_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._gen_timestamp()
        return timestamp
    
    def next_id(self):
        """生成下一个ID"""
        with self.lock:
            timestamp = self._gen_timestamp()
            
            # 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
            if timestamp < self.last_timestamp:
                raise Exception(f"Clock moved backwards. Refusing to generate id for {self.last_timestamp - timestamp} milliseconds")
            
            # 如果是同一时间生成的，则进行序列号自增
            if timestamp == self.last_timestamp:
                self.sequence = (self.sequence + 1) & self.sequence_mask
                # 序列号溢出，等待下一毫秒
                if self.sequence == 0:
                    timestamp = self._til_next_millis(self.last_timestamp)
            else:
                self.sequence = 0
            
            self.last_timestamp = timestamp
            
            return ((timestamp - self.twepoch) << self.timestamp_left_shift) | \
                   (self.datacenter_id << self.datacenter_id_shift) | \
                   (self.worker_id << self.worker_id_shift) | \
                   self.sequence


snowflake = Snowflake()


def get_next_id():
    return snowflake.next_id()


if __name__ == '__main__':
    snowflake = Snowflake()
    
    for _ in range(10):
        unique_id = snowflake.next_id()
        print(f"生成的ID: {unique_id} (二进制: {bin(unique_id)})")
