import os
import yaml
from typing import Dict, Any, Optional
from backend.src.core.logging import get_module_logger
logger = get_module_logger(__name__)

def load_yaml_config(file_path: str) -> Dict[str, Any]:
    """加载YAML配置文件"""
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                logger.info(f"已加载配置文件: {file_path}")
                return config or {}
        else:
            return {}
    except Exception as e:
        logger.error(f"加载配置文件失败 {file_path}: {str(e)}")
        return {}

def apply_config_to_settings(config_data: Dict[str, Any]) -> None:
    """将配置应用到settings对象"""
    from .config import settings
    
    for key, value in config_data.items():
        if hasattr(settings, key):
            # 记录配置变更
            old_value = getattr(settings, key)
            if key in ["SILICONFLOW_API_KEY"]:  # 敏感信息不完整显示
                logger.info(f"更新配置 {key}: [敏感信息已隐藏]")
            else:
                logger.info(f"更新配置 {key}: {old_value} -> {value}")
            
            # 设置新值
            setattr(settings, key, value)
        else:
            logger.warning(f"忽略未知配置项: {key}")

def find_config_file(file_name: Optional[str] = None) -> Optional[str]:
    """查找配置文件"""
    # 默认配置文件名
    file_name = file_name or "config.yaml"
    
    # 配置文件可能的位置
    root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    config_paths = [
        os.path.join(os.getcwd(), file_name),         # 当前工作目录
        os.path.join(root_path, "config", file_name), # 项目config目录
        os.path.join(root_path, file_name)            # 项目根目录
    ]
    
    # 查找第一个存在的配置文件
    for path in config_paths:
        if os.path.exists(path):
            return path
    
    return None

def load_configurations() -> None:
    """加载所有配置并应用到settings对象"""
    # 1. 从配置文件加载配置
    config_file = os.environ.get("CONFIG_FILE")
    env_suffix = os.environ.get("COMPOSER_ENV", "")
    
    # 如果设置了环境名，尝试加载环境特定的配置
    if env_suffix:
        # 例如: config.prod.yaml, config.dev.yaml
        env_config_file = f"config.{env_suffix}.yaml"
        env_config_path = find_config_file(env_config_file)
        if env_config_path:
            env_config = load_yaml_config(env_config_path)
            apply_config_to_settings(env_config)
            logger.info(f"已加载环境配置 [{env_suffix}]: {env_config_path}")
    
    # 加载默认或指定的配置文件
    if config_file:
        config_path = find_config_file(config_file)
    else:
        config_path = find_config_file()
        
    if config_path:
        config_data = load_yaml_config(config_path)
        apply_config_to_settings(config_data)
        logger.info(f"已加载配置文件: {config_path}")
    else:
        logger.info("未找到配置文件，使用默认配置")
    
    # 2. 从环境变量加载配置(环境变量优先级高于配置文件)
    from .config import settings
    
    env_count = 0
    for key, value in os.environ.items():
        if hasattr(settings, key):
            try:
                # 根据属性的类型进行转换
                attr_type = type(getattr(settings, key))
                converted_value = value
                
                if attr_type == bool:
                    # 处理布尔类型
                    if value.lower() in ('true', 'yes', '1', 'y'):
                        converted_value = True
                    elif value.lower() in ('false', 'no', '0', 'n'):
                        converted_value = False
                elif attr_type == int:
                    # 处理整数类型
                    converted_value = int(value)
                elif attr_type == float:
                    # 处理浮点数类型
                    converted_value = float(value)
                
                # 设置属性值
                setattr(settings, key, converted_value)
                
                # 记录配置变更(敏感信息不显示完整内容)
                if key in ["SILICONFLOW_API_KEY"]:
                    logger.info(f"从环境变量加载配置: {key}=[敏感信息已隐藏]")
                else:
                    logger.info(f"从环境变量加载配置: {key}={converted_value}")
                
                env_count += 1
            except (ValueError, TypeError) as e:
                logger.warning(f"环境变量转换失败 {key}={value}: {str(e)}")
    
    if env_count > 0:
        logger.info(f"从环境变量加载了 {env_count} 个配置项")
    
    # 3. 处理特殊配置
    
    # 处理CORS配置(支持字符串格式的CORS配置)
    if isinstance(settings.BACKEND_CORS_ORIGINS, str):
        settings.BACKEND_CORS_ORIGINS = [i.strip() for i in settings.BACKEND_CORS_ORIGINS.split(",")]
        logger.debug(f"CORS设置: {settings.BACKEND_CORS_ORIGINS}")
    
    # 设置数据库URI和确保目录存在
    settings._ensure_directories()
