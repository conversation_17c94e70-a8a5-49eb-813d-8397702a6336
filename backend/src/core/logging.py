"""
日志配置模块 - 提供集中的日志配置
"""
import json
import logging
import os
import sys
from contextvars import Context<PERSON><PERSON>
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable

from loguru import logger

from .config import settings

trace_var: ContextVar[str] = ContextVar('trace_id', default="DEFAULT")

# 默认日志格式
DEFAULT_FORMAT = ("<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
                  "<level>{level: <8}</level> | "
                  "<blue>{extra[trace_id]}</blue> | "
                  "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                  "<level>{message}</level>")


def inject_trace_id(record):
    """从trace_var中获取trace_id，如果没有则使用默认值"""
    record["extra"]["trace_id"] = trace_var.get()


logger = logger.patch(inject_trace_id)


# JSON日志格式函数
def json_format(record: Dict[str, Any]) -> str:
    """
    将日志记录格式化为JSON字符串
    
    Args:
        record: 日志记录字典
        
    Returns:
        格式化的JSON字符串
    """
    log_data = {
        "timestamp": record["time"].strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
        "level": record["level"].name,
        "message": record["message"],
        "module": record["name"],
        "function": record["function"],
        "line": record["line"],
        "process_id": record["process"].id,
        "thread_id": record["thread"].id
    }

    # 添加异常信息（如果有）
    if record["exception"]:
        log_data["exception"] = {
            "type": record["exception"].type,
            "value": str(record["exception"].value),
            "traceback": record["exception"].traceback
        }

    # 添加额外字段
    if record["extra"]:
        log_data["extra"] = record["extra"]

    return json.dumps(log_data)


class Rotator:
    def __init__(self, size_limit, time_limit):
        self.size_limit = size_limit
        self.time_limit = time_limit
        self.next_rotation = datetime.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        ) + timedelta(days=1)

    def should_rotate(self, message, file):
        # 检查文件大小
        file.seek(0, 2)
        if file.tell() >= self.size_limit:
            return True

        # 检查日期
        now = datetime.now()
        if now >= self.next_rotation:
            self.next_rotation = now.replace(
                hour=0, minute=0, second=0, microsecond=0
            ) + timedelta(days=1)
            return True

        return False


# 创建 Rotator 实例（5MB 和每天）
rotator = Rotator(size_limit=5 * 1024 * 1024, time_limit=timedelta(days=1))


def _client_log_filter(record):
    record_path = ""
    if hasattr(record["file"], "path"):
        record_path = record["file"].path
    elif isinstance(record["file"], (str,)):
        record_path = record["file"]
    record_path = os.path.normpath(record_path)
    test_path = os.path.normpath(os.path.join("backend", "tests"))
    return record_path.find(test_path) != -1


def setup_logging(
        log_level: Optional[str] = None,
        log_to_console: bool = True,
        log_to_file: bool = True,
        log_format: str = DEFAULT_FORMAT,
        use_json_format: bool = False,
        log_file_name: Optional[str] = None
) -> None:
    """
    设置日志配置
    
    Args:
        log_level: 日志级别，如果为None则使用配置中的LOG_LEVEL
        log_to_console: 是否输出到控制台
        log_to_file: 是否输出到文件
        log_format: 日志格式
        use_json_format: 是否使用JSON格式
        log_file_name: 日志文件名，如果为None则使用'app_{日期}.log'
    """
    # 使用配置中的日志级别（如果未指定）
    if log_level is None:
        log_level = settings.LOG_LEVEL

    # 移除默认的处理器
    logger.remove()

    # 添加控制台处理器
    if log_to_console:
        logger.add(
            sys.stderr,
            format=json_format if use_json_format else log_format,
            level=log_level,
            colorize=not use_json_format,
            backtrace=True,
            diagnose=True
        )

    # 添加文件处理器
    if log_to_file:
        # 确保日志目录存在
        os.makedirs(settings.LOG_DIR, exist_ok=True)

        # 设置日志文件名
        client_log_file_name = log_file_name
        if log_file_name is None:
            today = datetime.now().strftime("%Y%m%d")
            log_file_name = f"app_{today}.log"
            client_log_file_name = f"client_{today}.log"

        log_file_path = os.path.join(settings.LOG_DIR, log_file_name)
        client_log_file_path = os.path.join(settings.LOG_DIR, client_log_file_name)

        logger.add(
            log_file_path,
            format=json_format if use_json_format else log_format,
            level=log_level,
            rotation=rotator.should_rotate,  # 当文件达到500MB时或者0点轮转
            retention="10 days",  # 保留10天的日志
            compression="zip",  # 压缩旧日志
            backtrace=True,
            diagnose=True
        )

        logger.add(
            client_log_file_path,
            format=json_format if use_json_format else log_format,
            level=log_level,
            rotation=rotator.should_rotate,  # 当文件达到500MB时或者0点轮转
            retention="10 days",  # 保留10天的日志
            compression="zip",  # 压缩旧日志
            backtrace=True,
            diagnose=True,
            filter=lambda record: _client_log_filter(record)
        )

        logger.info(f"日志文件路径: {log_file_path}")

    # 记录日志配置信息
    logger.info(f"日志级别设置为: {log_level}")
    logger.info(f"日志格式: {'JSON' if use_json_format else 'TEXT'}")


def get_module_logger(module_name: str) -> "logger":
    """
    获取模块专用的日志记录器，推荐在各模块中使用此函数获取logger
    
    Args:
        module_name: 模块名称，通常为 __name__
    
    Returns:
        配置好的logger实例，带有模块上下文
    
    使用示例:
        ```python
        from backend.src.core.logging import get_module_logger
        
        logger = get_module_logger(__name__)
        logger.info("这是一条日志信息")
        ```
    """
    return logger.bind(module=module_name)


def get_request_logger():
    """
    获取用于记录HTTP请求的日志器
    
    Returns:
        配置好的logger实例
    """
    # 确保请求日志目录存在
    request_log_dir = os.path.join(settings.LOG_DIR, "requests")
    os.makedirs(request_log_dir, exist_ok=True)

    # 设置请求日志文件
    today = datetime.now().strftime("%Y%m%d")
    request_log_file = os.path.join(request_log_dir, f"requests_{today}.log")

    # 创建请求日志记录器
    request_logger = logger.bind(request=True)

    return request_logger


def get_function_logger(func: Callable) -> "logger":
    """
    获取函数级别的日志记录器，适用于装饰器模式
    
    Args:
        func: 要记录日志的函数
        
    Returns:
        配置好的logger实例，带有函数上下文
    
    使用示例:
        ```python
        from backend.src.core.logging import get_function_logger
        
        def my_function():
            log = get_function_logger(my_function)
            log.info("函数执行开始")
            # 函数逻辑...
            log.info("函数执行结束")
        ```
    """
    module_name = func.__module__
    function_name = func.__name__
    return logger.bind(module=module_name, function=function_name)


def get_custom_logger(context: Dict[str, Any]) -> "logger":
    """
    获取带有自定义上下文的日志记录器
    
    Args:
        context: 自定义上下文字典
        
    Returns:
        配置好的logger实例，带有自定义上下文
    
    使用示例:
        ```python
        from backend.src.core.logging import get_custom_logger
        
        logger = get_custom_logger({"user_id": "123", "session": "abc"})
        logger.info("用户操作日志")
        ```
    """
    return logger.bind(**context)


def get_cli_logger(cli_name: str, log_level: str = "INFO") -> "logger":
    """
    为CLI工具获取日志记录器
    
    Args:
        cli_name: CLI工具名称，用于日志文件命名
        log_level: 日志级别
        
    Returns:
        配置好的logger实例
    """
    # 确保日志目录存在
    cli_log_dir = os.path.join(settings.LOG_DIR, "cli")
    os.makedirs(cli_log_dir, exist_ok=True)

    # 设置日志文件路径
    log_file = os.path.join(cli_log_dir, f"{cli_name}.log")

    # 创建CLI专用logger
    cli_logger = logger.bind(cli=cli_name)

    return cli_logger


# 这是标准日志模块与loguru的兼容层，用于支持使用logging的库
class InterceptHandler(logging.Handler):
    """
    拦截标准库logging的处理器，将日志重定向到loguru
    当项目中使用了依赖标准logging库的第三方库时，使用此处理器可以将所有日志统一到loguru
    """

    def __init__(self):
        super().__init__()
        # 执行基本配置
        self.setLevel(logging.NOTSET)

    def emit(self, record):
        # 将标准日志的记录重定向到loguru
        try:
            level = logger.level(record.levelname).name
            frame, depth = sys._getframe(6), 6
            while frame and frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1

            logger.opt(depth=depth, exception=record.exc_info).log(
                level, record.getMessage()
            )
        except Exception:
            self.handleError(record)


def setup_standard_logging_intercept():
    """
    设置标准库logging拦截，将所有standard logging日志重定向到loguru
    在应用启动时调用此函数，可以确保所有使用标准库logging的日志都会被loguru处理
    """
    # 配置根日志记录器
    handler = InterceptHandler()

    # 设置基本配置
    logging.basicConfig(
        handlers=[handler],
        level=logging.NOTSET,
        force=True
    )

    # 为所有现有的日志记录器配置处理程序
    for name in logging.root.manager.loggerDict.keys():
        logging.getLogger(name).handlers = []
        logging.getLogger(name).propagate = True
        logging.getLogger(name).setLevel(logging.NOTSET)

    # 配置根日志记录器
    logging.root.handlers = [handler]

    logger.info("已设置标准库logging拦截，所有logging日志将重定向到loguru")


# 导出logger实例，方便其他模块直接导入使用
app_logger = logger
