import os
from typing import List, Optional, Dict, Any
from urllib.parse import quote_plus
from pathlib import Path


class Settings:
    """应用配置类，包含所有系统配置项"""
    
    # 项目路径
    ROOT_PATH: str = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # API配置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "BitFunComposer"
 
    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = ["*"]
    
    # 日志配置
    LOG_LEVEL: str = "DEBUG"
    LOG_DIR: str = os.path.join(ROOT_PATH, "logs")

    #模型服务配置：volcanoengine|siliconflow|aliyuncs|moonshot|fastrouter
    LLM_SERVICE: str = "volcanoengine"
    TEMPERATURE: float = 0.3
    TOP_P: float = 0.8
    TOP_K: float = 1
    MAX_TOKENS: int = 4096
    REQUEST_TIMEOUT: float = 60.0
    MAX_RETRIES: int = 3
    RETRY_DELAY: float = 20.0

    SILICONFLOW_API_KEY: str = "sk-"
    SILICONFLOW_MODEL: str = "Pro/deepseek-ai/DeepSeek-V3"

    # VOLCANOENGINE_API_KEY: str = "ba1a55eb-f6ad-4bd0-88fb-4e3ab5f69722"
    VOLCANOENGINE_API_KEY: str = "db011622-3694-490d-81a7-65e4ac64373f"
    VOLCANOENGINE_MODEL: str = "kimi-k2-250711"

    ALIYUNCS_MODEL: str = "qwen3-coder-plus-2025-07-22"
    ALIYUNCS_API_KEY: str = ""

    MOONSHOT_MODEL: str = "kimi-k2-0711-preview"
    MOONSHOT_API_KEY: str = ""

    FASTROUTER_MODEL: str = "claude-sonnet-4-20250514"
    FASTROUTER_API_KEY: str = "sk-tD88pugtVmUpZSxLotX0L2CDMQIngvVuvbj9c3PsxzpF0jfX"

    ISOLATE_PLAN_AGENT: bool = False

    WORKER_ID = 1
    DATA_CENTER_ID = 2

    COMPOSER_IP = "***************"
    COMPOSER_PORT = "8888"

    KNOWLEDGE_BASE_IP = "***************"
    KNOWLEDGE_BASE_PORT = "8787"

    CONDENSE_ROUND = 50

    def __init__(self):
        # 确保必要的目录存在
        self._ensure_directories()
       
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        os.makedirs(self.LOG_DIR, exist_ok=True)

    
    def get_config(self) -> Dict[str, Any]:
        """获取所有配置项"""
        config = {}
        for key in dir(self):
            # 排除私有属性和方法
            if not key.startswith('_') and not callable(getattr(self, key)):
                config[key] = getattr(self, key)
        return config

# 创建全局settings实例
settings = Settings() 