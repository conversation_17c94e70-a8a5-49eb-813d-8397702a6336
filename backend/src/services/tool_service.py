import threading
import uuid
from typing import List, Dict, Any

from backend.src.tools.knowledge_tool import HarmonyKnowledgeTool
from ..core.logging import get_module_logger
from ..schemas.request_schema import ToolUseRequest
from ..session.session import Session
from ..tools import Tool<PERSON>oll<PERSON><PERSON>, BuiltInToolCollection, BaseTool, MCPClient, ToolResult, ThinkTool, BaseExecTool, \
    TaskManageTool, WaitTool
from ..utils.wrapper import async_time_mark

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)


class ToolService:
    tool_map: Dict[str, BaseTool] = {}

    session = None
    task_cnt = 0
    task_cnt_lock = threading.Lock()

    def __init__(self, session: Session):
        self.session = session

        # 端侧工具列表
        self.client_tools = []
        self.client_tool_collection = ToolCollection([])
        self.client_tool_map = self.client_tool_collection.get_tool_map()

        # 云侧mcp
        self.mcp_client: MCPClient = MC<PERSON>lient()

        # 云侧定义的工具列表
        self.builtin_tool_collection = BuiltInToolCollection([])
        self.builtin_tool_map = self.builtin_tool_collection.get_tool_map()

    def add_client_tool_by_prompt(self, client_tool_prompt: List[dict]):
        # 端侧工具列表
        self.client_tools = []
        self.init_client_tools_by_prompt(tool_prompts=client_tool_prompt)
        self.client_tool_collection: ToolCollection = ToolCollection(self.client_tools)
        self.client_tool_map = self.client_tool_collection.get_tool_map()

    def add_client_tool(self, client_tools: List[BaseExecTool]):
        self.client_tool_collection = BuiltInToolCollection(client_tools)
        self.client_tool_map = self.client_tool_collection.get_tool_map()

    def add_builtin_tool(self, builtin_tools: List[BaseExecTool]):
        self.builtin_tool_collection = BuiltInToolCollection(builtin_tools)
        self.builtin_tool_map = self.builtin_tool_collection.get_tool_map()

    def init_client_tools_by_prompt(self, tool_prompts: List[dict]):
        """
        端侧tool prompt转成BaseTool，添加到ToolService
        :param tool_prompts: 端侧传来的tool prompt
        """
        for tool in tool_prompts:
            tool_info = tool.get("function", {})
            if len(tool_info) == 0:
                continue
            try:
                tool = BaseTool(**tool_info)
            except Exception:
                logger.exception(f"不完整的tool prompt: {tool_info}")
                continue
            self.client_tools.append(tool)

    def generate_task_id(self):
        with self.task_cnt_lock:
            self.task_cnt += 1
            return f"{self.session.session_id}-{uuid.uuid4().hex[:4]}-{self.task_cnt:04d}"

    async def initialize_mcp_servers(self, mcp_config):
        logger.info("开始初始化mcp servers")
        await self.mcp_client.connect_to_server(mcp_config)
        mcp_tools = self.mcp_client.get_tools()
        logger.info(f"初始化mcp servers完成, 获取到{len(mcp_tools)}个工具")

    def get_tools_prompt(self, tool_names: List[str] = None) -> List[dict]:
        """
        返回端侧和云侧的tool prompt
        :param tool_names: 指定工具名称列表
        :return: tool prompt的List
        """
        client_tool_prompt = self.client_tool_collection.build_tool_prompt(tool_names=tool_names)
        builtin_tools_prompt = self.builtin_tool_collection.build_tool_prompt(tool_names=tool_names)
        mcp_tools_prompt = self.mcp_client.build_tool_prompt(tool_names=tool_names)
        return client_tool_prompt + builtin_tools_prompt + mcp_tools_prompt

    @async_time_mark
    async def execute(self, tool_name, tool_params: Dict[str, Any], timeout=600) -> ToolResult:
        """
        LLM决定要执行工具，入参由LLM提供
        """
        task_id = self.generate_task_id()
        if tool_name in self.client_tool_map:  # 端侧工具执行
            cond = threading.Condition()
            with self.session.tool_call_cond_lock:
                self.session.tool_call_cond[task_id] = cond
                logger.debug(f"add tool_call_cond {task_id=}, {cond=}")
            with cond:
                msg = ToolUseRequest.construct_from_param(session_data=self.session.session_id,
                                                          msg_id=task_id,
                                                          tool_name=tool_name,
                                                          params=tool_params)
                self.session.add_msg_to_send(msg.model_dump())
                cond.wait(timeout=timeout)
                self.session.tool_call_cond.pop(task_id)

            resp = self.session.fetch_received_msg(task_id)
            if not resp:
                tool_result = ToolResult(error="无响应结果")
            else:
                tool_result = ToolResult(**resp.data.msg.model_dump())
            return tool_result
        elif tool_name in self.builtin_tool_map:
            tool = self.builtin_tool_map[tool_name]
            tool_result = await tool.execute(**tool_params)
            return tool_result
        elif tool_name in self.mcp_client.get_tool_map():
            # todo 云侧mcp，直接执行，返回结果
            return ToolResult(content="", error="")
        else:
            return ToolResult(content="", error=f"不匹配的工具名称: {tool_name}")

def get_default_tool_service(session, client_tool_prompt):
    tool_service = ToolService(session=session)
    tool_service.add_builtin_tool([HarmonyKnowledgeTool(), ThinkTool(), *TaskManageTool().get_tools()])
    tool_service.add_client_tool_by_prompt(client_tool_prompt)
    return tool_service
