# -*- coding: utf-8 -*-
from typing import Optional

from ..core.exceptions import LLMServiceError
from ..core.logging import get_module_logger

from backend.src.llm.base import BaseLLMService
from backend.src.llm.siliconflow import SiliconFlowService
from backend.src.llm.volcanoengine import VolcanoEngineService
from backend.src.llm.aliyuncs import AliyuncsService
from backend.src.llm.moonshot import MoonshotService

from backend.src.core.config import settings
from backend.src.utils.error import LLMServiceError
from backend.src.llm.fastrouter import FastRouterService

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

class LLMServiceFactory:
    """LLM服务工厂类，用于创建不同类型的LLM服务实例"""
    
    @staticmethod
    def create_service(service_type: Optional[str] = None, model: Optional[str] = None) -> BaseLLMService:
        """
        创建LLM服务实例
        
        Args:
            service_type: 服务类型，可选值为'ollama'或'siliconflow'，
                         如果为None则使用配置中的默认值
            model: 模型名称，如果为None则使用配置中的默认值
                         
        Returns:
            LLM服务实例
            
        Raises:
            LLMServiceError: 如果服务类型不支持或配置不正确
        """
        # 如果未指定服务类型，使用配置中的默认值
        if service_type is None:
            service_type = settings.LLM_SERVICE
            
        service_type = service_type.lower()
        
        if service_type == 'siliconflow':
            if not settings.SILICONFLOW_API_KEY:
                raise LLMServiceError('未配置SiliconFlow API密钥')
                
            # 如果未指定模型，使用配置中的默认值
            siliconflow_model = model or settings.SILICONFLOW_MODEL
            logger.info(f'创建SiliconFlow服务实例，模型: {siliconflow_model}')
            return SiliconFlowService(
                api_key=settings.SILICONFLOW_API_KEY,
                model_name=siliconflow_model,
                temperature=settings.TEMPERATURE,
                top_p=settings.TOP_P,
                top_k=settings.TOP_K,
                max_tokens=settings.MAX_TOKENS,
                request_timeout=settings.REQUEST_TIMEOUT,
                max_retries=settings.MAX_RETRIES,
                retry_delay=settings.RETRY_DELAY
            )
        elif service_type == 'volcanoengine':
            if not settings.VOLCANOENGINE_API_KEY:
                raise LLMServiceError('未配置VolcanoEngine API密钥')

            # 如果未指定模型，使用配置中的默认值
            volcanoengine_model = model or settings.VOLCANOENGINE_MODEL
            logger.info(f'创建VolcanoEngine服务实例，模型: {volcanoengine_model}')
            return VolcanoEngineService(
                api_key=settings.VOLCANOENGINE_API_KEY,
                model_name=volcanoengine_model,
                temperature=settings.TEMPERATURE,
                top_p=settings.TOP_P,
                top_k=settings.TOP_K,
                max_tokens=settings.MAX_TOKENS,
                request_timeout=settings.REQUEST_TIMEOUT,
                max_retries=settings.MAX_RETRIES,
                retry_delay=settings.RETRY_DELAY
            )
        elif service_type == 'aliyuncs':
            if not settings.ALIYUNCS_API_KEY:
                raise LLMServiceError('未配置Aliyuncs API密钥')

            # 如果未指定模型，使用配置中的默认值
            aliyuncs_model = model or settings.ALIYUNCS_MODEL
            logger.info(f'创建Aliyuncs服务实例，模型: {aliyuncs_model}')
            return AliyuncsService(
                api_key=settings.ALIYUNCS_API_KEY,
                model_name=aliyuncs_model,
                temperature=settings.TEMPERATURE,
                top_p=settings.TOP_P,
                top_k=settings.TOP_K,
                max_tokens=settings.MAX_TOKENS,
                request_timeout=settings.REQUEST_TIMEOUT,
                max_retries=settings.MAX_RETRIES,
                retry_delay=settings.RETRY_DELAY
            )
        elif service_type == 'moonshot':
            if not settings.MOONSHOT_API_KEY:
                raise LLMServiceError('未配置Moonshot API密钥')

            # 如果未指定模型，使用配置中的默认值
            moonshot_model = model or settings.MOONSHOT_MODEL
            logger.info(f'创建Moonshot服务实例，模型: {moonshot_model}')
            return MoonshotService(
                api_key=settings.MOONSHOT_API_KEY,
                model_name=moonshot_model,
                temperature=settings.TEMPERATURE,
                top_p=settings.TOP_P,
                top_k=settings.TOP_K,
                max_tokens=settings.MAX_TOKENS,
                request_timeout=settings.REQUEST_TIMEOUT,
                max_retries=settings.MAX_RETRIES,
                retry_delay=settings.RETRY_DELAY
            )
        elif service_type == 'fastrouter':
            if not settings.FASTROUTER_API_KEY:
                raise LLMServiceError('未配置FastRouter API密钥')

            # 如果未指定模型，使用配置中的默认值
            fastrouter_model = model or settings.FASTROUTER_MODEL
            logger.info(f'创建FastRouter服务实例，模型: {fastrouter_model}')
            return FastRouterService(
                api_key=settings.FASTROUTER_API_KEY,
                model_name=fastrouter_model,
                temperature=settings.TEMPERATURE,
                top_p=settings.TOP_P,
                top_k=settings.TOP_K,
                max_tokens=settings.MAX_TOKENS,
                request_timeout=settings.REQUEST_TIMEOUT,
                max_retries=settings.MAX_RETRIES,
                retry_delay=settings.RETRY_DELAY
            )
        else:
            raise LLMServiceError(f'不支持的LLM服务类型: {service_type}')
    
            
    @staticmethod
    async def check_service_status(service_type: Optional[str] = None) -> dict:
        """
        检查LLM服务状态
        
        Args:
            service_type: 服务类型，可选值为'ollama'或'siliconflow'，
                         如果为None则使用配置中的默认值
                         
        Returns:
            服务状态信息
            
        Raises:
            LLMServiceError: 如果服务类型不支持或配置不正确
        """
        service = LLMServiceFactory.create_service(service_type)
        return await service.check_service_status() 