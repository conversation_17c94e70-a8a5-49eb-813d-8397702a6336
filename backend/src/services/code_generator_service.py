""""""
import traceback

from backend.src.brain.ego import QueryContext, Ego
from backend.src.brain.id.planner.planner import Planner
from backend.src.brain.superego import Superego
from backend.src.schemas.metrics_schema import StepType
from backend.src.services.tool_service import ToolService, get_default_tool_service
from backend.src.core.config import settings
from backend.src.session.session import Session
from backend.src.utils.wrapper import time_mark


class CodeGeneratorService:
    def __init__(self, session):
        self.session: Session = session
        self.executor = None
        self.behaviour_prompt = f"""# Please achieve user's requirements. You should:
- Reply in Chinese as default. Normally you will co-work with Chinese programmer. 
- Collaborating with users. You should ask user for guidance at key step.
"""

    async def execute(self, query):
        """"""
        # self.session.hippocampus.add_user_msg(self.behaviour_prompt)
        user_query = f"""# User query
{query.data.msg.query}
"""
        self.session.add_user_query(query.data.msg.query)
        self.session.hippocampus.add_user_msg(user_query)
        try:
            if settings.ISOLATE_PLAN_AGENT:
                planner = Planner(self.session)
                self.executor = planner
                planner.add_user_query(user_query)
                planner.add_client_tool(query.tools)
                await planner.run()
            else:
                tool_service = get_default_tool_service(session=self.session, client_tool_prompt=query.tools)

                context = QueryContext(user_query)
                ego = Ego(self.session, context, tool_service)
                self.executor = ego
                superego = Superego()
                await ego.run()
                superego.run() # 预留
        except Exception as e:
            traceback.print_exc()
            self.session.add_response_msg_to_send(f"{e}")
        finally:
            self.session.stop()
    @time_mark
    def supplement_info(self, info):
        self.session.hippocampus.add_user_msg(info)
        self.session.set_supplement_info(info)

    def feedback(self, info):
        self.supplement_info(info)
        # 记录用户的反馈信息
        time_usage = self.supplement_info.time_usage
        self.session.recorder.record(StepType.USER_FEEDBACK, input_data=info, time_usage=time_usage)

    async def go_on(self):
        if self.session.is_running:
            return
        self.session.go_on()
        try:
            await self.executor.go_on()
        except Exception as e:
            traceback.print_exc()
            self.session.add_response_msg_to_send(f"{e}")
        finally:
            self.session.stop()
