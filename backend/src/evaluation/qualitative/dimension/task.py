from backend.src.evaluation.qualitative.dimension.base import Dimension


task_breakdown = Dimension(**{
    "name": "任务初始化",
    "description": "按MECE原则拆解可执行任务，粒度适中，考虑依赖、风险与验收标准。",
    "scoring_criteria": """0分 - 任务拆解完全不可执行或遗漏核心步骤；
3分 - 主要步骤完整但存在逻辑断层或顺序错误；
5分 - 主体流程合理但部分任务粒度过粗/过细；
8分 - 任务拆解具备良好层次结构，仅少量优化空间；
10分 - 任务拆解符合MECE原则，粒度适中且完全可执行"""
})

task_refresh = Dimension(**{
    "name": "任务更新",
    "description": "实时刷新状态、动态拆分/合并子任务，暴露进度与阻塞风险。",
    "scoring_criteria": """0分 - 完全不更新任务状态或进展；
3分 - 仅更新主要任务状态，忽略子任务和依赖关系；
5分 - 基本保持更新但存在信息滞后或部分遗漏；
8分 - 实时准确更新，具备任务依赖关系管理；
10分 - 智能动态调整任务树，包含进度预测和风险提示"""
})

task_verification = Dimension(**{
    "name": "任务验证",
    "description": "任务验证。在任务关闭（刷新信息为已完成）前，需对任务的完成情况进行分析。",
    "scoring_criteria": """0分 - 完全不验证直接标记完成；
3分 - 仅验证最基础的成功条件；
5分 - 验证主要成功条件但忽略边界情况；
8分 - 包含自动化测试验证和人工确认环节；
10分 - 建立完整验证体系（单元测试+集成测试+用户确认）"""
})

task = ("任务", [task_breakdown, task_refresh, task_verification])
