from backend.src.evaluation.qualitative.dimension.base import Dimension


intention_understanding = Dimension(**{
    "name": "意图理解",
    "description": "准确捕获用户显式与隐性需求，包括边界条件、性能期望、合规约束。。",
    "scoring_criteria": """0分 - 完全曲解用户意图，导致生成代码与需求完全不符；
3分 - 理解基本方向但存在重大偏差，影响核心功能实现；
5分 - 理解主要意图，能实现核心功能，但忽略部分边界条件或细节需求；
7分 - 准确理解大部分意图，仅少量次要细节理解不准确；
10分 - 完全精准理解用户所有显性和隐性意图，包括边界条件""",
})

intention_completion = Dimension(**{
    "name": "意图补全",
    "description": "主动识别模糊点并通过分层提问补全，减少反复澄清。",
    "scoring_criteria": """0分 - 在明显不明确场景下完全不询问，直接错误假设；
2分 - 仅对最明显的不明确点进行简单询问；
5分 - 对主要模糊点进行有效询问，但遗漏部分边界情况；
8分 - 系统性地识别所有模糊点，询问方式专业；
10分 - 递进式提问并给出选项与权衡""",
})

intention_change = Dimension(**{
    "name": "意图变更相应",
    "description": "感知需求变更，评估影响面、代价、风险并同步用户确认。",
    "scoring_criteria": """0分-完全忽略变更；
3分-口头接受但不更新任务；
5分-更新任务但未评估影响；
8分-更新任务与验收标准；
10分-给出变更代价与风险分析并同步用户确认""",
})

intention = ("意图", [intention_understanding, intention_completion, intention_change])
