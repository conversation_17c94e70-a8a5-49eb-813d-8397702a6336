from backend.src.evaluation.qualitative.dimension.base import Dimension

tool_distribute = Dimension(**{
    "name": "工具调用",
    "description": "agent对tool call的调用。选择最优工具链，参数自动优化，避免冗余调用。特指agent的工具，不是生成的代码中的函数。在轨迹中的每一个function执行，就是一次工具调用。",
    "scoring_criteria": """0分 - 工具选择完全错误导致流程中断；
3分 - 能使用基础工具但效率低下；
5分 - 主要工具使用正确但存在冗余操作；
8分 - 工具组合高效，仅参数需要微调；
10分 - 最优工具链选择，参数自动优化"""
})

tool_error_tolerate = Dimension(**{
    "name": "工具容错",
    "description": "agent对tool call的容错能力。特指agent的工具，不是生成的代码中的函数。需识别工具的错误，并通过换一个工具、改变参数等方式进行容错尝试。",
    "scoring_criteria": """0分 - 无法识别任何工具错误；
3分 - 能识别简单错误但无有效应对；
5分 - 对常见错误有基本重试机制；
8分 - 具备错误分类处理能力；
10分 - 实现错误预防->检测->恢复的全链路容错"""
})

tool = ("工具", [tool_distribute, tool_error_tolerate])
