from backend.src.evaluation.qualitative.dimension.base import Dimension


code_dependencies_unstanding = Dimension(**{
    "name": "代码依赖理解",
    "description": "识别显式与隐式依赖，处理版本冲突，给出最小依赖集。",
    "scoring_criteria": """0分 - 完全无法识别依赖关系，导致编译/运行失败；
2分 - 仅能识别最直接的一级依赖；
5分 - 理解主要依赖但存在版本误判或非必要依赖；
7分 - 准确识别所有显式依赖，包括传递性依赖；
10分 - 能推断隐式依赖（如系统环境依赖）并处理版本冲突"""
})

code_data_structure_unstanding = Dimension(**{
    "name": "关键数据结构理解",
    "description": "掌握数据结构使用场景与性能特征，可安全重构并保持兼容。",
    "scoring_criteria": """0分 - 错误理解核心数据结构导致功能异常；
3分 - 识别基础数据结构但误解复杂关系；
5分 - 正确使用主要数据结构但忽略优化机会；
8分 - 准确掌握所有数据结构的应用场景；
10分 - 能重构改进数据结构设计并保持兼容性"""
})

code_process_unstanding = Dimension(**{
    "name": "业务逻辑流程理解",
    "description": "还原业务流程状态机，发现设计缺陷并提出优化。",
    "scoring_criteria": """0分 - 完全错误的主流程理解；
3分 - 理解主干流程但遗漏异常分支；
5分 - 掌握核心逻辑但存在条件判断偏差；
8分 - 准确还原业务流程状态机；
10分 - 能发现业务流程中的设计缺陷并提出优化"""
})

code_project_structure_unstanding = Dimension(**{
    "name": "工程架构与设计模式理解",
    "description": "准确识别架构风格、设计意图，能评估合理性并提出演进方案。",
    "scoring_criteria": """0分 - 无法识别基本架构模式；
2分 - 识别基础分层但误解模块关系；
5分 - 理解主要架构但忽略设计意图；
8分 - 准确解读架构决策和模式应用；
10分 - 能评估架构合理性并提出演进方案"""
})

code_contract_design = Dimension(**{
    "name": "接口/数据契约设计",
    "description": "为新增或修改的数据结构、API 设计合理且可扩展的契约。",
    "scoring_criteria": """0分-无契约或导致严重不兼容；
3分-仅满足当前需求但无扩展性；
5分-满足需求且与存量结构兼容；
8分-具备扩展字段及版本策略；
10分-提供向前/向后兼容的演进方案"""
})

code = ("代码与工程理解",
        [code_data_structure_unstanding, code_dependencies_unstanding,
         code_process_unstanding, code_project_structure_unstanding, code_contract_design])
