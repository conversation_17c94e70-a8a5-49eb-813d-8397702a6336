from backend.src.evaluation.qualitative.dimension.base import Dimension


user_friend = Dimension(**{
    "name": "用户友好性",
    "description": """agent是否对给用户展示了合适的中间过程，以及是否给予用户关键流程把控能力（即关键节点询问用户决策）。
背景信息补充：通过add_task/set_task_status等任务管理后的任务列表，会实时呈现给用户。""",
    "scoring_criteria": """0分 - 黑盒操作无任何交互；
3分 - 仅展示最终结果；
5分 - 关键节点简单通知；
8分 - 可视化执行流程+关键决策点确认；
10分 - 提供交互式调试环境和决策建议系统"""
})

error_interact = Dimension(**{
    "name": "异常交互",
    "description": "当工具或代码运行出错时，agent向用户提供清晰、可操作的反馈。",
    "scoring_criteria": """0分-无提示或误导；
3分-简单报错；
5分-给出错误原因；
8分-提供修复建议；
10分-自动尝试修复并引导用户确认"""
})

user = ("用户友好性", [user_friend, error_interact])
