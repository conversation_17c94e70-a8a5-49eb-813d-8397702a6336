import json
from pathlib import Path

import pandas as pd

from backend.src.core.logging import get_module_logger, trace_var
from backend.src.schemas.metrics_schema import ProcessData, TimeUsage, TokenUsage, StepType
from backend.src.utils.parser import Parser

logger = get_module_logger(__name__)


class Recorder:
    def __init__(self):
        self.step_count = 0
        self.start_time = None
        self.end_time = None
        self.record_list = []

    def set_start_time(self, start_time):
        self.start_time = start_time if self.start_time is None else self.start_time

    def set_end_time(self, end_time):
        self.end_time = end_time if self.end_time is None else self.end_time

    def record(self, step_type: StepType, time_usage: TimeUsage = None, input_data: dict | None = None,
               output_data: dict | None = None, token_usage: TokenUsage = None):
        trace_id = trace_var.get()
        step = self.step_count + 1
        record = ProcessData(trace_id=trace_id, step=step, step_type=step_type, step_input=input_data,
                             step_output=output_data, time_usage=time_usage, token_usage=token_usage)
        self.record_list.append(record)
        self.step_count += 1

    @staticmethod
    def parse_record_data(data):
        if isinstance(data, str):
            return data
        elif isinstance(data, dict):
            return json.dumps(data, indent=4, ensure_ascii=False)
        return ''

    def save_to_csv(self, filename: str = None):
        """
        使用 pandas DataFrame 将 record_list 导出为 CSV 文件
        
        Args:
            filename: CSV 文件名称
        """
        if not self.record_list:
            logger.info("记录列表为空，无法导出")

        dirpath = Path(__file__).parent.parent.parent.joinpath("logs/metrics")
        filepath = dirpath.joinpath(filename)
        if not filepath.exists():
            filepath.parent.mkdir(parents=True, exist_ok=True)

        try:
            # 准备数据列表
            data_list = []
            compile_success_cnt = 0
            compile_error_cnt = 0
            wait_user_time = 0
            last_record = None
            for i, record in enumerate(self.record_list):
                # 处理时间格式转换
                start_time_str = ''
                end_time_str = ''
                latency_seconds = 0

                if record.time_usage:
                    # 将时间戳转换为北京时间
                    if record.time_usage.start_time:
                        start_time_str = Parser.parse_timestamp_to_str(record.time_usage.start_time)

                    if record.time_usage.end_time:
                        end_time_str = Parser.parse_timestamp_to_str(record.time_usage.end_time)

                    if record.time_usage.latency:
                        latency_seconds = round(record.time_usage.latency, 3)

                # 统计编译成功、失败的次数
                if record.step_type==StepType.EXECUTION and isinstance(record.step_input, dict) and isinstance(record.step_output, dict):
                    if "check_compilation" in record.step_input.get("id", ""):
                        compile_content = record.step_output.get("content", "")
                        if "编译成功" in compile_content:
                            compile_success_cnt += 1
                        else:
                            compile_error_cnt += 1
                    if "show_msg_to_user_and_wait_for_response" in record.step_input.get("id", ""):
                        wait_user_time += record.time_usage.latency

                row_data = {
                    'trace_id': record.trace_id,
                    'step': record.step,
                    'step_type': record.step_type.value if record.step_type else '',
                    'step_input': self.parse_record_data(record.step_input),
                    'step_output': self.parse_record_data(record.step_output),
                    'start_time': start_time_str,
                    'end_time': end_time_str,
                    'latency(seconds)': latency_seconds,
                    'prompt_tokens': record.token_usage.prompt_tokens if record.token_usage else 0,
                    'completion_tokens': record.token_usage.completion_tokens if record.token_usage else 0,
                    'total_tokens': record.token_usage.total_tokens if record.token_usage else 0
                }
                # 记录用户补充需求的时间
                if record.step_type==StepType.USER_REQUEST and i > 0:
                    wait_user_time += record.time_usage.start_time - last_record.time_usage.end_time
                last_record = record
                data_list.append(row_data)

            # 创建 DataFrame
            df = pd.DataFrame(data_list)

            # 计算汇总数据
            total_latency = round(self.end_time - self.start_time - wait_user_time, 3)
            total_prompt_tokens = df['prompt_tokens'].sum() if 'prompt_tokens' in df.columns else 0
            total_completion_tokens = df['completion_tokens'].sum() if 'completion_tokens' in df.columns else 0
            total_all_tokens = df['total_tokens'].sum() if 'total_tokens' in df.columns else 0

            # 添加汇总行
            summary_row = {
                'trace_id': trace_var.get(),
                'step': self.get_records_count(),
                'step_type': StepType.SUMMARY,
                'step_input': f'编译成功次数: {compile_success_cnt}\n编译失败次数: {compile_error_cnt}',
                'step_output': '',
                'start_time': Parser.parse_timestamp_to_str(self.start_time),
                'end_time': Parser.parse_timestamp_to_str(self.end_time),
                'latency(seconds)': total_latency,
                'prompt_tokens': total_prompt_tokens,
                'completion_tokens': total_completion_tokens,
                'total_tokens': total_all_tokens,
            }

            # 将汇总行添加到 DataFrame
            df = pd.concat([df, pd.DataFrame([summary_row])], ignore_index=True)

            # 保存原始CSV
            df.to_csv(filepath, index=False, encoding='utf-8')

            logger.info(f"成功导出 {len(self.record_list)} 条记录（含汇总行）到 {filepath}")
            return filepath

        except Exception as e:
            logger.info(f"导出 CSV 文件时出错: {e}")
            return None

    def clear_records(self):
        """
        清空记录列表
        """
        self.record_list.clear()
        self.step_count = 0
        logger.info("已清空所有记录")

    def get_records_count(self):
        """
        获取记录数量
        
        Returns:
            int: 记录数量
        """
        return len(self.record_list)

