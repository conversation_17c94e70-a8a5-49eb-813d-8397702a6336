import asyncio

from backend.src.core.logging import get_module_logger
from backend.src.llm.prompts import PromptTemplates
from backend.src.services.llm_service import LLMServiceFactory

logger = get_module_logger(__name__)

class SelfEvaluation:
    @staticmethod
    def evaluate_system_prompt(evaluation_result):
        system_prompt = """你是一个“代码生成agent”的专家，可以对“代码生成agent”的system prompt进行分析、评价并优化。"""
        prompt = f"""这是“代码生成agent”的system prompt，以及对于这个agent的能力评测结果。请对prompt进行分析并优化，分别给出缺点、优化思路和优化后prompt。

# 注意事项
agent的整体目标是成为鸿蒙应用代码生成助手，可辅助开发者在鸿蒙应用工程中进行代码生成。

评测结果是针对以一次交互的分析，不要在system prompt中为这个case进行过度优化。保持泛华性。

# system prompt
{PromptTemplates().get_system_prompt()}

# agent评测结果
{evaluation_result}

"""
        messages = [
            {
                "role": "system",
                "content": system_prompt
            }, {
                "role": "user",
                "content": prompt
            }
        ]
        logger.debug(f"message is {messages}")
        llm = LLMServiceFactory.create_service()

        content, _, _ = asyncio.run(llm.call_with_context(messages=messages))
        logger.debug(f"content is {content}")

if __name__ == "__main__":
    evaluation_result = """[
{
    "name": "用户友好性",
    "score": 3,
    "score_reason": "在整个实现过程中，Agent 仅在最终阶段一次性输出「任务完成总结」，中间过程（如方案设计、代码修改、编译错误修复）均未向用户展示，也未在关键节点（如是否采用持久化存储、是否更新 BusinessPage 展示逻辑、是否修复编译错误）主动询问用户确认。用户处于“黑盒”状态，无法感知执行进度或进行干预。",
    "problem": "1. 无中间过程可视化：用户看不到任务拆分、设计文档、代码改动详情。2. 无关键决策确认：Agent 直接替用户做了技术选型（Preferences 存储）、UI 样式、错误修复方案等决定。3. 无交互式调试：出现编译错误后，Agent 自行修复，未让用户知情或选择修复策略。",
    "advice": "1. 在「需求分析→方案设计」后，主动输出设计文档并询问用户“是否确认此方案？”。2. 每完成一个子任务（如新增 SignInService、修改 SignInPage、修改 BusinessPage、修复编译错误）后，用简洁 Markdown 摘要+diff 片段向用户汇报，并询问“是否继续下一步？”。3. 出现编译错误时，提供两种修复策略（例如：禁用解构、调整异步签名）让用户选择，而非直接修改。4. 最终交付前，提供交互式检查清单让用户逐项确认功能点。"
}
]"""
    logger.info(SelfEvaluation.evaluate_system_prompt(evaluation_result))
