import json
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional

from backend.src.tools.base import BaseExecTool, ToolResult
from backend.src.core.config import settings

from backend.src.core.logging import get_module_logger
logger = get_module_logger(__name__)

HARMONY_KNOWLEDGE_URL = f"http://{settings.KNOWLEDGE_BASE_IP}:{settings.KNOWLEDGE_BASE_PORT}/api/v1/knowledge/search"

class HarmonyKnowledgeTool(BaseExecTool):
    name: str = "search_harmony_knowledge"
    description: str = """ 
    使用此工具可以在鸿蒙知识库中检索开发相关的信息，支持查询鸿蒙API及最佳实践。
    适用场景：
    - 当你不确定该使用哪个API，需要获得指导时；
    - 当你希望参考业界最佳实践进行实现时；
    - 当你需要了解某个鸿蒙API的详细用法时。
    输入参数：
    - query: 字符串，需要在鸿蒙知识库中检索的内容
    - domain: 字符串，需要检索的领域，默认值为'api_documentation'，可选值为'best_practices'和'api_documentation'，不可输入其他值
    输出格式：
    - 返回鸿蒙知识库中检索到的相关知识条目列表，每个条目包含id、title、content、url、relevance、type、knowledge_id等字段
    """
    parameters: dict = {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "用于检索鸿蒙知识库的查询内容。例如：如何在ArkTS中使用@State装饰器？",
            },
            "domain": {
                "type": "string",
                "description": "需要检索的领域，只能选一个，默认值为api_documentation, 可选best_practices和api_documentation",
                "default": "api_documentation"
            }
        },
        "required": ["query"]
    }

    async def execute(self, **kwargs) -> ToolResult:
        """
        执行Harmony知识库查询
        
        Args:
            query: 查询字符串
            
        Returns:
            ToolResult: 包含查询结果的工具执行结果
            
        The response is a list of knowledge items.
        Each knowledge item is a dictionary with the following keys:
        - **id**: 知识条目或知识块ID
        - **title**: 知识标题
        - **content**: 知识内容
        - **url**: 知识来源URL（可能为null）
        - **relevance**: 相关度分数（0-1之间）
        - **type**: 结果类型，document（整个文档）或chunk（文档片段）
        - **knowledge_id**: 如果是chunk，所属的知识条目ID
        """
        query = kwargs.get("query", "").strip()
        
        if not query or len(query) < 3:
            return ToolResult(
                error="查询内容太少，请至少输入3个字符"
            )
        
        search_params = {
            "limit": 5,
            "min_score": 0.5,
            "use_multi_query": True,
            "query": query,
            "domains": [kwargs.get("domain", "api_documentation")]
        }
        url = HARMONY_KNOWLEDGE_URL
        
        logger.info(f"search knowledge url: {url}, params: {search_params}")
        
        try:
            # 发送HTTP请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    json=search_params,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        return ToolResult(
                            error=f"知识库查询服务请求失败 (状态码: {response.status}): {error_text}"
                        )
                    
                    knowledge_list = await response.json()
                    logger.info(f"search knowledge result: {knowledge_list}")
            
            # 处理和格式化结果
            formatted_result = self._format_knowledge_results(query, knowledge_list)
            
            return ToolResult(
                content=formatted_result
            )
            
        except aiohttp.ClientError as e:
            return ToolResult(
                error=f"网络请求失败: {str(e)}"
            )
        except asyncio.TimeoutError:
            return ToolResult(
                error="请求超时，请检查知识库服务是否正常运行"
            )
        except json.JSONDecodeError as e:
            return ToolResult(
                error=f"响应格式解析失败: {str(e)}"
            )
        except Exception as e:
            return ToolResult(
                error=f"执行查询时发生错误: {str(e)}"
            )
    
    def _format_knowledge_results(self, query: str, knowledge_list: List[Dict[str, Any]]) -> str:
        """
        格式化知识库查询结果
        
        Args:
            query: 原始查询字符串
            knowledge_list: 从知识库服务返回的结果列表
            
        Returns:
            str: 格式化后的结果字符串
        """
        if not knowledge_list:
            return f"查询: {query}\n\n未找到相关的知识条目"
        
        formatted_output = [f"查询: {query}"]
        formatted_output.append(f"找到 {len(knowledge_list)} 个相关知识条目\n")
        
        for i, knowledge in enumerate(knowledge_list, 1):
            # 提取知识条目信息
            knowledge_id = knowledge.get("id", "未知ID")
            title = knowledge.get("title", "无标题")
            content = knowledge.get("content", "无内容")
            url = knowledge.get("url")
            relevance = knowledge.get("relevance", 0)
            result_type = knowledge.get("type", "unknown")
            parent_knowledge_id = knowledge.get("knowledge_id")
            
            # 格式化单个结果
            result_section = [
                f"知识条目 {i}:",
                f"   ID: {knowledge_id}",
                f"   标题: {title}",
                f"   类型: {result_type}",
                f"   相关度: {relevance * 100:.1f}%"
            ]
            
            if parent_knowledge_id:
                result_section.append(f"   所属文档: {parent_knowledge_id}")
            
            if url:
                result_section.append(f"   来源: {url}")
            
            result_section.append(f"   内容: {content}")

            formatted_output.extend(result_section)
        
        return "\n".join(formatted_output)
    
    async def health_check(self, url: str = HARMONY_KNOWLEDGE_URL) -> bool:
        """
        检查知识库服务健康状态
        
        Args:
            url: 服务URL
            
        Returns:
            bool: 服务是否健康
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    json={"query": "test", "limit": 1},
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    return response.status == 200
        except:
            return False

# 工具使用示例
async def example_usage():
    """使用示例"""
    tool = HarmonyKnowledgeTool()
    
    # 示例1: 健康检查
    health = await tool.health_check()
    print(f"示例1 - 健康检查: {'✅ 服务正常' if health else '❌ 服务异常'}")
    print("\n" + "="*80 + "\n")

    # 示例2: 基本查询
    result1 = await tool.execute(query="@State decorator")
    print("示例2 - 基本查询:")
    print(result1.content if result1.error is None else f"错误: {result1.error}")
    print("\n" + "="*80 + "\n")
    
    # 示例3: 带@knowledge前缀的查询
    result2 = await tool.execute(query="@knowledge ArkTS component")
    print("示例3 - 前缀查询:")
    print(result2.content if result2.error is None else f"错误: {result2.error}")
    print("\n" + "="*80 + "\n")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(example_usage())

