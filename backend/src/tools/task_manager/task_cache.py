from backend.src.tools.task_manager.task import Task

class TaskCache:
    def __init__(self):
        self.task_list: list[Task] = []

    def get_task_list(self):
        return self.task_list

    def get_task_by_id(self, task_id: int):
        for task in self.task_list:
            if task.id == task_id:
                return task
        return None

    def add_task(self, task):
        self.task_list.append(task)

    def move_task(self, from_id, to_id):
        from_index = -1
        for i, task in enumerate(self.task_list):
            if task.id == from_id:
                from_index = i
                break
        if from_index == -1:
            return f"待修改的任务id【{from_id}】不存在"

        original_task = self.task_list[from_index]
        self.task_list.pop(from_index)
        if to_id == -1:
            self.task_list.insert(0, original_task)
            return "插入成功"

        insert_index = -1
        for i, task in enumerate(self.task_list):
            if task.id == to_id:
                insert_index = i + 1
                break
        if insert_index == -1:
            return f"待插入后面的任务id【{to_id}】不存在"

        self.task_list.insert(insert_index, original_task)
        return "插入成功"
