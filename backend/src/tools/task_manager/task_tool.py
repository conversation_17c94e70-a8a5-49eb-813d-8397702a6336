import asyncio
import json

from backend.src.core.logging import get_module_logger
from backend.src.tools.base import BaseExecTool, ToolResult
from backend.src.tools.task_manager.task import Task, TaskStatus, SubTask
from backend.src.tools.task_manager.task_cache import TaskCache
from backend.src.utils.parser import Parser

logger = get_module_logger(__name__)

# 任务管理系列的用法和综述，写到system prompt内
class AddTaskTool(BaseExecTool):
    name: str = "add_task"
    description: str = """[Task Management]之增加一个任务。调用时机：
1-刚接收到用户请求时。对用户请求进行任务拆解，并记录。
2-任务执行过程中，发现需要其他步骤，可增加子任务。"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "id": {
                "type": "number",
                "description": """任务id。可从0开始计数。""",
            },
            "name": {
                "type": "string",
                "description": """任务标题名称""",
            },
            "description": {
                "type": "string",
                "description": """任务描述""",
            },
            "status": {
                "type": "string",
                "description": """任务状态。可从“待执行”，“进行中”，“已完成”中选择。默认为待执行。""",
            },
            "dependencies": {
                "type": "array",
                "description": """本任务的依赖，需要等待其他任务完成后，再执行本任务。默认无依赖。""",
                "items": {
                    "type": "number",
                    "description": """依赖的任务的id"""
                },
            },
            "priority": {
                "type": "string",
                "description": """优先级。可从“高”“中”“低”中选择。默认优先级中。""",
            },
            "test_strategy": {
                "type": "string",
                "description": """验证策略。需要明确如何验证任务完成。""",
            }
        },
        "required": ["id", "name", "description", "test_strategy"],
        "additionalProperties": False,
    }
    task_cache: TaskCache = None

    def __init__(self, task_cache: TaskCache):
        super().__init__()
        self.task_cache = task_cache

    async def execute(self, **kwargs) -> ToolResult:
        """
        """
        task = Task(**kwargs)
        self.task_cache.add_task(task)

        return ToolResult(content="新增任务ok")


class GetTasksTool(BaseExecTool):
    name: str = "get_tasks"
    description: str = """[Task Management]之查看任务列表。
"""
    parameters: dict = {
        "type": "object",
        "properties": {
        },
        "required": [],
        "additionalProperties": False,
    }
    task_cache: TaskCache = None

    def __init__(self, task_cache: TaskCache):
        super().__init__()
        self.task_cache = task_cache

    async def execute(self, **kwargs) -> ToolResult:
        """
        """
        return ToolResult(content=json.dumps([task.model_dump() for task in self.task_cache.get_task_list()], ensure_ascii=False))


class SetTaskStatusTool(BaseExecTool):
    name: str = "set_task_status"
    description: str = """[Task Management]之修改任务状态。调用时机：
1-在每任务执行完成后，应调用本方法以刷新任务进展状态。
2-若修改的是子任务，需同时传入任务id与子任务id。"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "id": {
                "type": "number",
                "description": """待修改的任务id。若待修改的是子任务，此处传入子任务的主任务id。""",
            },
            "sub_id": {
                "type": "number",
                "description": """待修改的子任务id。如果要修改的是子任务的状态时填入。""",
            },
            "status": {
                "type": "string",
                "description": """待修改为的任务状态。可从“待执行”，“进行中”，“已完成”中选择。""",
            },
            "result_summary": {
                "type": "string",
                "description": """任务完成情况与任务执行结果。这将指导后续任务的执行与规划，**务必**保留关键信息，控制在200字以内。""",
            }
        },
        "required": ["id", "status"],
        "additionalProperties": False,
    }
    task_cache: TaskCache = None

    def __init__(self, task_cache: TaskCache):
        super().__init__()
        self.task_cache = task_cache

    async def execute(self, **kwargs) -> ToolResult:
        """
        """
        task_id = kwargs.get("id", 0)
        sub_id = kwargs.get("sub_id")
        status = kwargs.get("status", "")
        execution_result = kwargs.get("execution_result", None)

        task = self.task_cache.get_task_by_id(task_id)
        if task:
            if sub_id:
                sub_task = task.get_sub_task(sub_id)
                if sub_task:
                    sub_task.update_status(status, execution_result)
                    return ToolResult(content=f"已修改任务【{task_id}】的子任务【{sub_task}】状态为【{status}】")
                else:
                    return ToolResult(error=f"未找到任务【{task_id}】的子任务【{sub_task}】。")
            task.update_status(status, execution_result)
            return ToolResult(content=f"已修改任务【{task_id}】状态为【{status}】")
        else:
            return ToolResult(error=f"未找到id为{task_id}的任务。")

class CheckDependencySatisfiedTool(BaseExecTool):
    name: str = "check_dependency_satisfied"
    description: str = """[Task Management]之检查任务的依赖项是否满足。"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "id": {
                "type": "number",
                "description": """待检查的任务id。""",
            }
        },
        "required": ["id"],
        "additionalProperties": False,
    }
    task_cache: TaskCache = None

    def __init__(self, task_cache: TaskCache):
        super().__init__()
        self.task_cache = task_cache

    async def execute(self, **kwargs) -> ToolResult:
        """
        """
        task_id = kwargs.get("id", 0)

        task = self.task_cache.get_task_by_id(task_id)
        if task:
            to_check_tasks = task.dependencies
            logger.debug(f"to check tasks: {to_check_tasks}")
            for to_check_task in to_check_tasks:
                # 不考虑未找到依赖任务的情况，依赖任务和合理性需要task反序列化时进行check
                if self.task_cache.get_task_by_id(to_check_task).status != TaskStatus.COMPLETED.value:
                    return ToolResult(content=f"依赖任务【{to_check_task}】未完成，处在状态【{self.task_cache.get_task_by_id(to_check_task).status}】中。")
            return ToolResult(content=f"依赖任务皆已完成")
        else:
            return ToolResult(error=f"未找到id为{task_id}的任务。")


class MoveTaskTool(BaseExecTool):
    name: str = "move_task"
    description: str = """[Task Management]之修改任务位置排序。调用时机：需要改变任务顺序时，如“新增任务并应先于存量任务执行时”。"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "id": {
                "type": "number",
                "description": """待修改的任务id。""",
            },
            "to": {
                "type": "number",
                "description": """需要迁移到哪个任务后面的任务id。若需要迁移到任务列表最前面，输入“-1”。""",
            }
        },
        "required": ["id", "to"],
        "additionalProperties": False,
    }
    task_cache: TaskCache = None

    def __init__(self, task_cache: TaskCache):
        super().__init__()
        self.task_cache = task_cache

    async def execute(self, **kwargs) -> ToolResult:
        """
        """
        task_id = kwargs.get("id", 0)
        to_id = kwargs.get("to", 0)

        return ToolResult(content=self.task_cache.move_task(task_id, to_id))


class AddSubTaskTool(BaseExecTool):
    name: str = "add_sub_task"
    description: str = """[Task Management]之新增子任务。当任务粒度过大需要拆分时，调用本工具拆分细粒度子任务，增加每任务的可达成性。原始任务拆分后仍存在。"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "from_task_id": {
                "type": "number",
                "description": """待拆分的原始任务id。原始任务拆分后仍存在。""",
            },
            "id": {
                "type": "number",
                "description": """新增的子任务的任务id。可按照【{原始任务id}.{子任务顺序}】格式，如任务2的第一个子任务制定id为 2.1 """,
            },
            "name": {
                "type": "string",
                "description": """子任务标题名称""",
            },
            "description": {
                "type": "string",
                "description": """子任务描述""",
            },
            "status": {
                "type": "string",
                "description": """子任务状态。可从“待执行”，“进行中”，“已完成”中选择。默认为待执行。""",
            },
            "dependencies": {
                "type": "array",
                "description": """子任务的依赖，需要等待其他任务完成后，再执行本任务。默认无依赖。""",
                "items": {
                    "type": "number",
                    "description": """依赖的任务的id"""
                },
            },
            "priority": {
                "type": "string",
                "description": """子任务优先级。可从“高”“中”“低”中选择。默认优先级中。""",
            },
            "test_strategy": {
                "type": "string",
                "description": """子任务验证策略。需要明确如何验证任务完成。""",
            }
        },
        "required": ["from_task_id", "id", "name", "description", "test_strategy"],
        "additionalProperties": False,
    }
    task_cache: TaskCache = None

    def __init__(self, task_cache: TaskCache):
        super().__init__()
        self.task_cache = task_cache

    async def execute(self, **kwargs) -> ToolResult:
        """
        """
        sub_task = SubTask(**Parser.exclude_fields_from_json(kwargs, ["from_task_id"]))
        from_task_id = kwargs.get("from_task_id", -1)

        task = self.task_cache.get_task_by_id(from_task_id)
        if task:
            task.add_subtask(sub_task)
            return ToolResult(content=f"已在任务{from_task_id}添加子任务{sub_task.id}")
        else:
            return ToolResult(error=f"未找到id为{from_task_id}的任务。")


class TaskManageTool:
    def __init__(self):
        self.task_cache: TaskCache = TaskCache()
        self.tool_list = []
        self.tool_list.append(AddTaskTool(self.task_cache))
        self.tool_list.append(GetTasksTool(self.task_cache))
        self.tool_list.append(SetTaskStatusTool(self.task_cache))
        self.tool_list.append(CheckDependencySatisfiedTool(self.task_cache))
        self.tool_list.append(MoveTaskTool(self.task_cache))
        self.tool_list.append(AddSubTaskTool(self.task_cache))

    def get_tools(self):
        return self.tool_list


if __name__ == "__main__":
    tools = TaskManageTool().get_tools()

    logger.info(asyncio.run(tools[0].execute(**{
        "id": 0,
        "name": "name",
        "description": "description",
        "test_strategy": "test_strategy"
    })))

    logger.info(asyncio.run(tools[1].execute(**{})))
    logger.info(asyncio.run(tools[2].execute(**{
        "id": 1,
        "status": "进行中",
    })))
    logger.info(asyncio.run(tools[1].execute(**{})))
    logger.info(asyncio.run(tools[0].execute(**{
        "id": 1,
        "name": "name",
        "description": "description",
        "test_strategy": "test_strategy",
        "dependencies": [0]
    })))
    logger.info(asyncio.run(tools[3].execute(**{
        "id": 1,
    })))
    logger.info(asyncio.run(tools[4].execute(**{
        "id": 1,
        "to": -1
    })))
    logger.info(asyncio.run(tools[1].execute(**{})))
