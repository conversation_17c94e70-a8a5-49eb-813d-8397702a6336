import json
from enum import Enum
from typing import List, Dict, Optional

from pydantic import BaseModel

class TaskStatus(Enum):
    """任务状态枚举"""
    NOT_STARTED = "待执行"
    IN_PROGRESS = "进行中"
    COMPLETED = "已完成"
    BLOCKED = "已阻塞"
    CANCELLED = "已取消"

class Priority(Enum):
    """优先级枚举"""
    HIGH = "高"
    MEDIUM = "中"
    LOW = "低"

class SubTask(BaseModel):
    id: float
    name: str
    description: str = ""
    status: str = "待执行"
    dependencies: List[float] = []
    priority: str = "中"
    test_strategy: str = ""
    execution_result: str = ""
    def update_status(self, new_status, execution_result) -> None:
        """更新任务状态"""
        self.status = new_status
        if execution_result:
            self.execution_result = execution_result


# TODO: 增加status、priority合法数值校验，增加dependencies有效性校验
class Task(BaseModel):
    id: float
    name: str
    description: str = ""
    status: str = "待执行"
    dependencies: List[float] = []
    priority: str = "中"
    test_strategy: str = ""
    subtasks: List[SubTask] = []
    execution_result: str = ""

    def add_subtask(self, subtask: SubTask) -> None:
        """添加子任务"""
        self.subtasks.append(subtask)

    def remove_subtask(self, subtask_id: float) -> bool:
        """移除子任务，返回是否成功"""
        for i, subtask in enumerate(self.subtasks):
            if subtask.id == subtask_id:
                self.subtasks.pop(i)
                return True
        return False

    def get_sub_task(self, subtask_id: float):
        for subtask in self.subtasks:
            if subtask.id == subtask_id:
                return subtask
        return None

    def update_status(self, new_status, execution_result) -> None:
        """更新任务状态"""
        self.status = new_status
        if execution_result:
            self.execution_result = execution_result
