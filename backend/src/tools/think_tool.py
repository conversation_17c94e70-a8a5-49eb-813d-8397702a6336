import asyncio
import json

from pydantic import BaseModel

from backend.src.core.logging import get_module_logger
from backend.src.tools.base import BaseExecTool, ToolResult

logger = get_module_logger(__name__)

class ThoughtData(BaseModel):
    thought: str
    thought_number: int
    total_thoughts: int
    next_thought_needed: bool
    is_revision: bool | None = None
    revises_thought: int | None = None
    branch_from_thought: int | None = None
    branch_id: str | None = None
    needs_more_thoughts: bool | None = None


class ThinkTool(BaseExecTool):
    name: str = "deep_think"
    description: str = """ 
通过思考动态反思解决问题的详细工具。
该工具有助于通过灵活的思维过程来分析问题，这种思维过程可以不断调整和发展。
随着理解的加深，每次思考都可以对之前的见解进行补充、质疑或修正。

何时使用此工具
- 将复杂问题分解为若干步骤
- 有修改余地的规划和设计
- 可能需要修正方向的分析
- 最初可能不清楚全部范围的问题
- 需要多步骤解决方案的问题
- 需要在多个步骤中保持上下文的任务
- 需要过滤无关信息的情况

主要功能
- 您可以在工作过程中调整 “total_thoughts”，也可以减少 “total_thoughts”。
- 可以质疑或修改之前的想法
- 即使在似乎已经结束的情况下，也可以添加更多想法
- 可以表达不确定性并探索其他方法
- 并非每个想法都需要线性构建--可以分支或回溯
- 生成解决方案假设
- 根据思维链步骤验证假设
- 重复这一过程，直到满意为止
- 提供正确答案

你应该
1. 首先对所需想法进行初步估计，但随时准备调整
2. 随时质疑或修改之前的想法
3. 必要时，即使是在 “最后”，也要毫不犹豫地添加更多想法
4. 表达不确定性
5. 标出修正先前思路或进入新路径的想法
6. 忽略与当前步骤无关的信息
7. 适时提出解决方案假设
8. 根据思维链步骤验证假设
9. 重复该过程，直到对解决方案满意为止
10. 提供一个理想的正确答案作为最终输出结果
11. 只有在真正完成并得到满意答案时，才将 next_thought_needed 设为 false
    """
    parameters: dict = {
        "type": "object",
        "properties": {
            "thought": {
                "type": "string",
                "description": """你当前的思考步骤，可以包括
* 常规分析步骤
* 修改以前的想法
* 对之前决定的质疑
* 意识到需要更多分析
* 改变方法
* 提出假设
* 假设验证""",
            },
            "next_thought_needed": {
                "type": "boolean",
                "description": "是否需要进一步思考。如果需要更多思考，即使看起来已经结束，则为 True。",
            },
            "thought_number": {
                "type": "number",
                "description": """当前思考序列中的顺序。如果需要，可以大于先前的total_thoughts。从1开始计数。"""
            },
            "total_thoughts": {
                "type": "number",
                "description": """当前估计需要思考的次数。可调整。从1开始计数。"""
            },
            "is_revision": {
                "type": "boolean",
                "description": "该想法是否修改了之前的思考",
            },
            "revises_thought": {
                "type": "number",
                "description": "编号几的思考进行了重思考。如果 is_revision 为 true，则需要此参数。",
            },
            "branch_from_thought": {
                "type": "number",
                "description": "如果是分支，哪个思考编号是分支点。",
            },
            "needs_more_thoughts": {
                "type": "boolean",
                "description": "如果已到达思考终点，但意识到需要更多想法",
            }
        },
        "required": ["thought", "next_thought_needed", "thought_number", "total_thoughts", "is_revision", "needs_more_thoughts"]
    }
    thought_history: list[ThoughtData] = []

    branches: dict[str, list[ThoughtData]] = {}

    def __init__(self):
        super().__init__()
        self.thought_history: list[ThoughtData] = []
        self.branches: dict[str, list[ThoughtData]] = {}

    async def execute(self, **kwargs) -> ToolResult:
        """

        """
        validated_input = ThoughtData(**kwargs)

        # Adjust total thoughts if current thought number exceeds it
        if validated_input.thought_number > validated_input.total_thoughts:
            validated_input.total_thoughts = validated_input.thought_number

        # Add to thought history
        self.thought_history.append(validated_input)

        # Handle branching
        if validated_input.branch_from_thought and validated_input.branch_id:
            if validated_input.branch_id not in self.branches:
                self.branches[validated_input.branch_id] = []
            self.branches[validated_input.branch_id].append(validated_input)

        # Prepare response
        response_data = {
            "thought_number": validated_input.thought_number,
            "total_thoughts": validated_input.total_thoughts,
            "next_thought_needed": validated_input.next_thought_needed,
            "branches": list(self.branches.keys()),
            "thought_history_length": len(self.thought_history)
        }
            
        return ToolResult(content=json.dumps(response_data, ensure_ascii=False))


if __name__ == "__main__":
    think_tool = ThinkTool()
    logger.info(asyncio.run(think_tool.execute(**{
        "thought": "thought",
        "thought_number": 3,
        "total_thoughts": 3,
        "next_thought_needed": True,
        "is_revision": True,
        "revises_thought": 2,
        "branch_from_thought": 2,
        "needs_more_thoughts": True,
    })))
