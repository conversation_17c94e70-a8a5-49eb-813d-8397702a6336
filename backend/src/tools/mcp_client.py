from typing import Optional, Any, List

from fastmcp import Client
from fastmcp.client.transports import infer_transport

from .base import BaseExecTool, ToolCollection
from ..core.logging import get_module_logger

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)


class MCPClientTool(BaseExecTool):
    session: Optional[Client] = None

    async def execute(self, **kwargs) -> Any:
        await self.session.call_tool(self.name, **kwargs)


class MCPClient(ToolCollection):
    client: Optional[Client] = None
    _tools: List[BaseExecTool] = []

    def __init__(self):
        super().__init__(tools=[])

    async def connect_to_server(self, mcp_config: dict[str, Any]):
        transport = infer_transport(mcp_config)
        self.client = Client(transport, timeout=30)
        await self.__initialize_and_list_tools__()

    async def __initialize_and_list_tools__(self):
        if self.client is None:
            raise RuntimeError("Client is not initialized")

        async with self.client:
            await self.client.ping()
            mcp_tools = await self.client.list_tools()
        for tool in mcp_tools:
            server_tool = MCPClientTool(
                name=tool.name,
                description=tool.description,
                parameters=tool.inputSchema,
                session=self.client
            )
            self._tools.append(server_tool)
        super().add_tools(self._tools)
        logger.info(f"Available tools: {', '.join([t.name for t in mcp_tools])}")
