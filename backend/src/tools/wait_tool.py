import asyncio
import json

from pydantic import BaseModel

from backend.src.core.logging import get_module_logger
from backend.src.tools.base import BaseExecTool, ToolResult

logger = get_module_logger(__name__)

class WaitTool(BaseExecTool):
    name: str = "wait_for_user"
    description: str = """等待用户回复消息。当你需要等待用户回复时，调用此工具。 
    """
    parameters: dict = {
        "type": "object",
        "properties": {},
        "required": []
    }

    async def execute(self, **kwargs) -> ToolResult:
        """
        """
        if self.session is None:
            return ToolResult(error="无法暂停")
        self.session.stop()
        return ToolResult(content="已暂停，等待用户回复。")


if __name__ == "__main__":
    wait_tool = WaitTool(session=None)
    logger.info(asyncio.run(wait_tool.execute()))
