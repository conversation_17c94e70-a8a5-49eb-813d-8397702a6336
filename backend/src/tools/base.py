from abc import ABC, abstractmethod
from typing import List, Any, Dict
from typing import Optional

from pydantic import BaseModel, Field

from backend.src.session.session import Session


class BaseTool(ABC, BaseModel):
    name: str
    description: str | None = None
    parameters: Optional[dict] = None
    session: Optional[Session] = None

    model_config = {
        "arbitrary_types_allowed": True  # 允许任意类型
    }

    def to_param(self) -> dict:
        """ Convert to LLM function call format"""
        def add_default_instruction_param(parameters):
            parameters["properties"]["instruction"] = {
                "type": "string",
                "description": "调用此工具的背景原因，和期望得到的结果"
            }
            parameters["required"].append("instruction")
            return parameters
        return {
            "type": "function",
            "function":
                {
                    "name": self.name,
                    "description": self.description,
                    "parameters": add_default_instruction_param(self.parameters)
                }
        }


class ToolResult(BaseModel):
    content: Any = Field(default=None)
    error: Optional[str] = Field(default=None)


class BaseExecTool(BaseTool, ABC):
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """ Execute the tool with given parameters. """

    async def __call__(self, **kwargs) -> ToolResult:
        try:
            return await self.execute(**kwargs)
        except Exception as e:
            return ToolResult(error=str(e))


class ToolCollection:
    _tools: List[BaseTool] = []
    _tool_map: Dict[str, BaseTool] = {}

    def __init__(self, tools: List[BaseTool]) -> None:
        self._tools = tools or []
        self._tool_map = {tool.name: tool for tool in self._tools}

    def get_tool(self, tool_name: str, default: Optional[BaseTool] = None) -> Optional[BaseTool]:
        return self._tool_map.get(tool_name, default)

    def get_tool_map(self):
        return self._tool_map

    def get_tools(self, tool_names: List[str] = None) -> List[Optional[BaseTool]]:
        if tool_names is None or len(tool_names) == 0:
            return self._tools
        else:
            return [tool for tool in (self.get_tool(tool_name) for tool_name in tool_names) if tool is not None]

    def build_tool_prompt(self, tools: List[BaseTool] = None, tool_names: List[str] = None) -> List[dict]:
        target_tools = tools if tools is not None else (self.get_tools(tool_names) if tool_names else self._tools)
        return [tool.to_param() for tool in target_tools if tool is not None]

    def add_tool(self, tool: BaseTool):
        self._tools.append(tool)
        self._tool_map[tool.name] = tool

    def add_tools(self, tools: List[BaseTool]):
        self._tools.extend(tools)
        self._tool_map.update({tool.name: tool for tool in tools})

    async def execute(self, tool_name, **kwargs) -> ToolResult:
        tool = self.get_tool(tool_name)
        if isinstance(tool, BaseExecTool):
            return await tool.execute(**kwargs)
        else:
            return ToolResult(error="Only BaseExecTool can be executed.")
