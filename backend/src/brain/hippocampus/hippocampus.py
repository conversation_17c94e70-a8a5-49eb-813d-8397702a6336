"""
短期记忆管理
"""
import json
from pathlib import Path

from backend.src.brain.hippocampus.condense_prompt import condense_system_prompt
from backend.src.core.config import settings
from backend.src.core.logging import get_module_logger
from backend.src.services.llm_service import LLMServiceFactory

logger = get_module_logger(__name__)


class Memory:
    def __init__(self, init_memory: list[dict]):
        self.memory = init_memory.copy()
        self.skip_first = 1  # Remain user initial requirement

    def get_memory(self):
        return self.memory

    def add_content_history(self, content):
        self.memory.append({
            "role": "assistant",
            "content": content
        })

    def add_tool_call_history(self, content, tool_calls):
        self.memory.append({
            "role": "assistant",
            "content": content,
            "tool_calls": tool_calls
        })

    def add_assistant_content_history(self, content):
        self.memory.append({
            "role": "assistant",
            "content": content
        })

    def add_tool_result_history(self, tool_call_id, content: str):
        self.memory.append({
            "role": "tool",
            "content": content,
            "tool_call_id": tool_call_id
        })

    def add_user_msg(self, content):
        self.memory.append({
            "role": "user",
            "content": content
        })

    def replace_middle_section(self, start, end, replacement):
        """
        替换数组中指定范围的元素
        """
        self.memory = self.memory[:start] + replacement + self.memory[end + 1:]

    @staticmethod
    def find_valid_tool_sequence_end(json_array, start_index):
        """
        从 start_index 开始向后查找，找到第一个满足条件的 index，
        使得从 0 到该 index 的范围内，所有 tool_calls 和 tool_responses 完全匹配。

        参数:
            json_array: 包含消息的JSON数组
            start_index: 开始查找的索引

        返回:
            int: 满足条件的最小 index，如果找不到则返回 None
        """
        pending_calls = {}  # 存储未匹配的 tool_calls，key 为 id，value 为 tool_call

        for i in range(len(json_array)):
            item = json_array[i]

            if item["role"] == "assistant" and "tool_calls" in item:
                # 处理 assistant 的 tool_calls
                for call in item["tool_calls"]:
                    call_id = call["id"]
                    pending_calls[call_id] = call

            elif item["role"] == "tool" and "tool_call_id" in item:
                # 处理 tool 的 response
                call_id = item["tool_call_id"]
                if call_id in pending_calls:
                    del pending_calls[call_id]

            # 检查是否已经满足条件（所有 pending_calls 都被匹配）
            if i >= start_index and not pending_calls:
                return i

        # 如果遍历完整个数组仍未找到满足条件的 index
        return None

    async def compress_memory(self):
        """压缩记忆，滑动窗口压缩"""
        if len(self.memory) < settings.CONDENSE_ROUND:
            return

        condense_range = (self.skip_first, self.find_valid_tool_sequence_end(self.memory,
                                                               len(self.memory) / 2))

        forgotten_events = self.memory[condense_range[0]:condense_range[1]]
        prompt = condense_system_prompt + f"""\n\n\nThe context to condense is below.
<context>
{forgotten_events}
</context>
Now summarize the events using the rules above.
"""
        context = [
            {
                "role": "user",
                "content": prompt
            }
        ]

        llm = LLMServiceFactory.create_service()
        logger.debug(f"message is {context}")

        content, _, tokens = await llm.call_with_context(messages=context)

        logger.debug(f"condensed context is {content}, \nrange is {condense_range}")

        self.replace_middle_section(condense_range[0], condense_range[1], [{
            "role": "assistant",
            "content": f"压缩过的历史上下文信息：\n{content}"
        }])

    def export_to_file(self, filename: str) -> None:
        dirpath = Path(__file__).parent.parent.parent.parent.joinpath("logs/memory")
        filepath = dirpath.joinpath(filename)
        if not filepath.exists():
            filepath.parent.mkdir(parents=True, exist_ok=True)
        try:
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(json.dumps(self.memory, indent=4, ensure_ascii=False))
        except Exception as e:
            logger.error(f"export_to_file error: {e}")


class Hippocampus:
    def __init__(self):
        self.memory = Memory([])
        # 原始记忆
        self.original_memory = Memory([])

    def get_original_memory(self):
        """返回未压缩的记忆"""
        return self.original_memory

    def get_complete_memory(self):
        """返回完整短期记忆"""
        return self.memory

    def get_goal_related_memory(self, goal):
        """根据目标，提取相关记忆。需要包含【原始诉求】、【前序动作总结】、【目标相关清晰记忆】、【etc.】"""
        return self.memory

    def add_tool_call_history(self, content, tool_calls):
        self.memory.add_tool_call_history(content, tool_calls)
        self.original_memory.add_tool_call_history(content, tool_calls)

    def add_assistant_content_history(self, content):
        self.memory.add_assistant_content_history(content)
        self.original_memory.add_assistant_content_history(content)

    def add_tool_result_history(self, tool_call_id, content):
        self.memory.add_tool_result_history(tool_call_id, content)
        self.original_memory.add_tool_result_history(tool_call_id, content)

    def add_user_msg(self, content):
        self.memory.add_user_msg(content)
        self.original_memory.add_user_msg(content)

    def add_content_history(self, content):
        self.memory.add_content_history(content)
        self.original_memory.add_content_history(content)


if __name__ == '__main__':
    import asyncio
    memory = [{'role': 'assistant', 'content': '滴滴滴，启动完成，美好的一天又开始啦！让我先检查下自身服务是否运行正常。', 'tool_calls': [{'function': {'arguments': '{}', 'name': 'check_self_status'}, 'id': 'call_8p0opi8ghdy4bpwktfzohian', 'type': 'function'}]}, {'role': 'tool', 'content': '运行正常。', 'tool_call_id': 'call_8p0opi8ghdy4bpwktfzohian'}, {'role': 'assistant', 'content': '太好了，今天又可以开开心心上班了！啊，来客人了！'}, {'role': 'user', 'content': '请帮我实现以下请求：\n<请求>\n实现点击历史订单页面的历史订单后跳转到订单详情页面的能力。我不想看代码，这个工程是由一个页面生成的agent实现的，我也不知道实现了哪些代码，我只能看到应用的显示结果。。工程目录在 C:\\Users\\<USER>\\DevEcoStudioProjects\\MyApplication3\n</请求>\n<要求>\n1-我喜欢做事有条理的人，请使用通过拆分任务（任务管理）的方式来调理你的步调；先列计划，再执行，再在执行中调整计划；\n2-我想要参与进来，重要的决策要请示我，关键环节要等我批准；\n3-请帮我写优秀的代码，多分析下代码架构，多翻阅下资料。\n</要求>\n'}, {'role': 'assistant', 'content': '哇，欢迎欢迎！我是小方，今天来帮你实现历史订单跳转到订单详情的功能～\n\n让我先了解一下这个工程的现状，然后咱们一起制定一个有条理的计划！', 'tool_calls': [{'index': 0, 'id': 'list_dir:1', 'type': 'function', 'function': {'name': 'list_dir', 'arguments': '{"root_directory": "C:\\\\Users\\\\<USER>\\\\DevEcoStudioProjects\\\\MyApplication3", "instruction": "查看工程整体结构，了解项目布局"}'}}]}, {'role': 'tool', 'content': '{"content": "C:\\\\Users\\\\<USER>\\\\DevEcoStudioProjects\\\\MyApplication3\\n    ├── AppScope\\n    ├──     └── resources\\n    ├──     └──     └── base\\n    ├──     ├── app.json5\\n    ├── entry\\n    ├──     └── src\\n    ├──     └──     ├── main\\n    ├──     └──     ├──     ├── module.json5\\n    ├──     └──     ├── mock\\n    ├──     └──     ├──     └── mock-config.json5\\n    ├──     └──     ├── ohosTest\\n    ├──     └──     ├──     ├── module.json5\\n    ├──     └──     └── test\\n    ├──     └──     └──     ├── List.test.ets\\n    ├──     └──     └──     └── LocalUnit.test.ets\\n    ├──     ├── build-profile.json5\\n    ├──     ├── hvigorfile.ts\\n    ├──     ├── obfuscation-rules.txt\\n    ├──     ├── oh-package.json5\\n    └── hvigor\\n    └──     └── hvigor-config.json5\\n    ├── build-profile.json5\\n    ├── code-linter.json5\\n    ├── hvigorfile.ts\\n    ├── local.properties\\n    ├── oh-package-lock.json5\\n    ├── oh-package.json5", "error": null}', 'tool_call_id': 'list_dir:1'}, {'role': 'assistant', 'content': '让我再深入看看主要的代码结构：', 'tool_calls': [{'index': 0, 'id': 'list_dir:2', 'type': 'function', 'function': {'name': 'list_dir', 'arguments': '{"root_directory": "C:\\\\Users\\\\<USER>\\\\DevEcoStudioProjects\\\\MyApplication3\\\\entry\\\\src\\\\main", "instruction": "查看主要代码目录结构"}'}}]}, {'role': 'tool', 'content': '{"content": "C:\\\\Users\\\\<USER>\\\\DevEcoStudioProjects\\\\MyApplication3\\\\entry\\\\src\\\\main\\n    ├── ets\\n    ├──     ├── common\\n    ├──     ├──     ├── BreakpointConstants.ets\\n    ├──     ├──     ├── CommonConstants.ets\\n    ├──     ├──     ├── CommonEnums.ets\\n    ├──     ├──     ├── ModuleTitleComponent.ets\\n    ├──     ├──     └── ModuleTitleComponentWithIcon.ets\\n    ├──     ├── entryability\\n    ├──     ├──     └── EntryAbility.ets\\n    ├──     ├── entrybackupability\\n    ├──     ├──     └── EntryBackupAbility.ets\\n    ├──     ├── pages\\n    ├──     ├──     ├── HomePage.ets\\n    ├──     ├──     ├── OrderHistoryPage.ets\\n    ├──     ├──     ├── OrderPage.ets\\n    ├──     ├──     └── ProductPage.ets\\n    ├──     ├── util\\n    ├──     ├──     ├── BreakpointType.ets\\n    ├──     ├──     ├── LazyDataSource.ets\\n    ├──     ├──     ├── Logger.ets\\n    ├──     ├──     ├── ObservedArray.ets\\n    ├──     ├──     ├── WebUtils.ets\\n    ├──     ├──     └── WindowUtil.ets\\n    ├──     ├── view\\n    ├──     ├──     ├── CategoryTabModule.ets\\n    ├──     ├──     ├── EmptyOrderHintModule.ets\\n    ├──     ├──     ├── EmptyPage.ets\\n    ├──     ├──     ├── EmptyPagePathStack.ets\\n    ├──     ├──     ├── FoodBannerModule.ets\\n    ├──     ├──     ├── FoodCarouselModule.ets\\n    ├──     ├──     ├── HotCategoryTagsModule.ets\\n    ├──     ├──     ├── HotRecommendationModule.ets\\n    ├──     ├──     ├── LoadingFailedView.ets\\n    ├──     ├──     ├── LoadingView.ets\\n    ├──     ├──     ├── NavigationModule.ets\\n    ├──     ├──     ├── NewProductLaunchModule.ets\\n    ├──     ├──     ├── OrderAreaModule.ets\\n    ├──     ├──     ├── OrderFilterTabModule.ets\\n    ├──     ├──     ├── OrderListCardModule.ets\\n    ├──     ├──     ├── OrderListModule.ets\\n    ├──     ├──     ├── OrderStatusFilterModule.ets\\n    ├──     ├──     ├── ProductCategoryModule.ets\\n    ├──     ├──     ├── ProductDisplayModule.ets\\n    ├──     ├──     ├── ProductListDisplayModule.ets\\n    ├──     ├──     ├── ProductListModule.ets\\n    ├──     ├──     ├── ProductRecommendationModule.ets\\n    ├──     ├──     ├── PromotionBannerModule.ets\\n    ├──     ├──     ├── QuickAccessModule.ets\\n    ├──     ├──     ├── QuickOrderModule.ets\\n    ├──     ├──     ├── RecommendedProductsModule.ets\\n    ├──     ├──     ├── SearchAndScanModule.ets\\n    ├──     ├──     ├── SearchDishModule.ets\\n    ├──     ├──     ├── SearchModuleWithButtonPage.ets\\n    ├──     ├──     ├── SecondPageSearch.ets\\n    ├──     ├──     ├── ShoppingCartIndicatorModule.ets\\n    ├──     ├──     └── StoreInfoModule.ets\\n    ├──     └── viewmodel\\n    ├──     └──     ├── EmptyOrderHintModuleData.ets\\n    ├──     └──     ├── FoodBannerModuleData.ets\\n    ├──     └──     ├── FoodCarouselModuleData.ets\\n    ├──     └──     ├── HotCategoryTagsModuleData.ets\\n    ├──     └──     ├── HotRecommendationModuleData.ets\\n    ├──     └──     ├── NavigationModuleData.ets\\n    ├──     └──     ├── NewProductLaunchModuleData.ets\\n    ├──     └──     ├── OrderAreaModuleData.ets\\n    ├──     └──     ├── OrderListCardModuleData.ets\\n    ├──     └──     ├── OrderListModuleData.ets\\n    ├──     └──     ├── ProductDisplayModuleData.ets\\n    ├──     └──     ├── ProductListDisplayModuleData.ets\\n    ├──     └──     ├── ProductListModuleData.ets\\n    ├──     └──     ├── ProductRecommendationModuleData.ets\\n    ├──     └──     ├── PromotionBannerModuleData.ets\\n    ├──     └──     ├── QuickAccessModuleData.ets\\n    ├──     └──     ├── QuickOrderModuleData.ets\\n    ├──     └──     ├── RecommendedProductsModuleData.ets\\n    ├──     └──     ├── SearchAndScanModuleData.ets\\n    ├──     └──     └── ShoppingCartIndicatorModuleData.ets\\n    ├──     ├── Index.ets\\n    └── resources\\n    └──     ├── base\\n    └──     ├──     ├── element\\n    └──     ├──     ├──     ├── color.json\\n    └──     ├──     ├──     ├── color_dark.json\\n    └──     ├──     ├──     ├── color_light.json\\n    └──     ├──     ├──     ├── float.json\\n    └──     ├──     ├──     ├── font_size.json\\n    └──     ├──     ├──     └── string.json\\n    └──     ├──     ├── media\\n    └──     ├──     ├──     ├── background.png\\n    └──     ├──     ├──     ├── foreground.png\\n    └──     ├──     ├──     ├── goodsImg.png\\n    └──     ├──     ├──     ├── ic_back.svg\\n    └──     ├──     ├──     ├── ic_failure.svg\\n    └──     ├──     ├──     ├── ic_public_app.svg\\n    └──     ├──     ├──     ├── ic_public_more_black.svg\\n    └──     ├──     ├──     ├── ic_right_arrow_filled.svg\\n    └──     ├──     ├──     ├── icon.png\\n    └──     ├──     ├──     ├── img_goods_logo_1.png\\n    └──     ├──     ├──     ├── layered_image.json\\n    └──     ├──     ├──     ├── lib_icon_0.svg\\n    └──     ├──     ├──     ├── lib_icon_173.svg\\n    └──     ├──     ├──     ├── lib_icon_182.svg\\n    └──     ├──     ├──     ├── lib_icon_196.svg\\n    └──     ├──     ├──     ├── lib_icon_212.svg\\n    └──     ├──     ├──     ├── lib_icon_218.svg\\n    └──     ├──     ├──     ├── lib_icon_221.svg\\n    └──     ├──     ├──     ├── lib_icon_50.svg\\n    └──     ├──     ├──     ├── lib_icon_65.svg\\n    └──     ├──     ├──     ├── lib_icon_8.svg\\n    └──     ├──     ├──     ├── noImage.png\\n    └──     ├──     ├──     ├── realImageGen_common_0.png\\n    └──     ├──     ├──     ├── realImageGen_common_1.png\\n    └──     ├──     ├──     ├── realImageGen_common_2.png\\n    └──     ├──     ├──     ├── realImageGen_common_3.png\\n    └──     ├──     ├──     ├── realImageGen_food_1.png\\n    └──     ├──     ├──     ├── realImageGen_food_10.png\\n    └──     ├──     ├──     ├── realImageGen_food_11.png\\n    └──     ├──     ├──     ├── realImageGen_food_12.png\\n    └──     ├──     ├──     ├── realImageGen_food_14.png\\n    └──     ├──     ├──     ├── realImageGen_food_15.png\\n    └──     ├──     ├──     ├── realImageGen_food_17.png\\n    └──     ├──     ├──     ├── realImageGen_food_18.png\\n    └──     ├──     ├──     ├── realImageGen_food_2.png\\n    └──     ├──     ├──     ├── realImageGen_food_4.png\\n    └──     ├──     ├──     ├── realImageGen_food_6.png\\n    └──     ├──     ├──     ├── realImageGen_food_9.png\\n    └──     ├──     ├──     ├── realImageGen_shop_0.png\\n    └──     ├──     ├──     ├── realImageGen_shop_1.png\\n    └──     ├──     ├──     ├── realImageGen_shop_2.png\\n    └──     ├──     ├──     ├── realImageGen_shop_3.png\\n    └──     ├──     ├──     ├── realImageGen_shop_4.png\\n    └──     ├──     ├──     ├── realImageGen_shop_5.png\\n    └──     ├──     ├──     ├── realImageGen_shop_6.png\\n    └──     ├──     ├──     ├── realImageGen_travel_0.png\\n    └──     ├──     ├──     ├── star.svg\\n    └──     ├──     ├──     └── startIcon.png\\n    └──     ├──     └── profile\\n    └──     ├──     └──     ├── backup_config.json\\n    └──     ├──     └──     └── main_pages.json\\n    └──     ├── dark\\n    └──     ├──     └── element\\n    └──     ├──     └──     └── color.json\\n    └──     └── rawfile\\n    ├── module.json5", "error": null}', 'tool_call_id': 'list_dir:2'}, {'role': 'assistant', 'content': '太好了！我已经看到了清晰的工程结构。现在让我先分析一下现有的代码，然后制定一个详细的任务计划。\n\n让我先看看历史订单页面和订单详情页面的代码：', 'tool_calls': [{'index': 0, 'id': 'view_file:3', 'type': 'function', 'function': {'name': 'view_file', 'arguments': '{"absolute_path": "C:\\\\Users\\\\<USER>\\\\DevEcoStudioProjects\\\\MyApplication3\\\\entry\\\\src\\\\main\\\\ets\\\\pages\\\\OrderHistoryPage.ets", "instruction": "查看历史订单页面代码，了解当前结构"}'}}]}, {'role': 'tool', 'content': '{"content": "import { OrderStatusFilterModule } from \'../view/OrderStatusFilterModule\';\\nimport { OrderListModule } from \'../view/OrderListModule\';\\n\\n@Component\\nexport struct OrderHistoryPage {\\n  private scroller: Scroller = new Scroller();\\n\\n  build() {\\n    Flex({ direction: FlexDirection.Column }) {\\n      Scroll(this.scroller) {\\n        Column() {\\n          OrderStatusFilterModule()\\n\\t\\t\\t\\t\\tOrderListModule()\\n        }\\n        .width(\'100%\')\\n        .justifyContent(FlexAlign.Center)\\n        .alignItems(HorizontalAlign.Center)\\n      }\\n      .scrollable(ScrollDirection.Vertical)\\n      .scrollBar(BarState.Off)\\n      .width(\'100%\')\\n    }\\n    .backgroundColor($r(\'app.color.page_bg_color\'))\\n  }\\n", "error": null}', 'tool_call_id': 'view_file:3'}, {'role': 'assistant', 'content': '', 'tool_calls': [{'index': 0, 'id': 'view_file:4', 'type': 'function', 'function': {'name': 'view_file', 'arguments': '{"absolute_path": "C:\\\\Users\\\\<USER>\\\\DevEcoStudioProjects\\\\MyApplication3\\\\entry\\\\src\\\\main\\\\ets\\\\pages\\\\OrderPage.ets", "instruction": "查看订单详情页面代码，了解当前结构"}'}}]}, {'role': 'tool', 'content': '{"content": "import { SearchDishModule } from \'../view/SearchDishModule\';\\nimport { CategoryTabModule } from \'../view/CategoryTabModule\';\\nimport { ProductDisplayModule } from \'../view/ProductDisplayModule\';\\n\\n@Component\\nexport struct OrderPage {\\n  private scroller: Scroller = new Scroller();\\n\\n  build() {\\n    Flex({ direction: FlexDirection.Column }) {\\n      Scroll(this.scroller) {\\n        Column() {\\n          SearchDishModule()\\n\\t\\t\\t\\t\\tCategoryTabModule()\\n\\t\\t\\t\\t\\tProductDisplayModule()\\n        }\\n        .width(\'100%\')\\n        .justifyContent(FlexAlign.Center)\\n        .alignItems(HorizontalAlign.Center)\\n      }\\n      .scrollable(ScrollDirection.Vertical)\\n      .scrollBar(BarState.Off)\\n      .width(\'100%\')\\n    }\\n    .backgroundColor($r(\'app.color.page_bg_color\'))\\n  }\\n", "error": null}', 'tool_call_id': 'view_file:4'}, {'role': 'assistant', 'content': '让我再看看订单列表模块，看看历史订单是如何显示的：', 'tool_calls': [{'index': 0, 'id': 'view_file:5', 'type': 'function', 'function': {'name': 'view_file', 'arguments': '{"absolute_path": "C:\\\\Users\\\\<USER>\\\\DevEcoStudioProjects\\\\MyApplication3\\\\entry\\\\src\\\\main\\\\ets\\\\view\\\\OrderListModule.ets", "instruction": "查看订单列表模块，了解订单项的展示方式"}'}}]}, {'role': 'tool', 'content': '{"content": "import { DataSource, DataSourceStyleAStyleA, DataSourceStyleBStyleB } from \'../viewmodel/OrderListModuleData\';\\nimport { LoadingStatus } from \'../common/CommonEnums\';\\nimport { LoadingFailedView } from \'./LoadingFailedView\';\\nimport LazyDataSource from \'../util/LazyDataSource\';\\n\\n@Component\\nstruct OrderItemCard {\\n  @Prop storeName: string = \'\';\\n  @Prop storeTag: string = \'\';\\n  @Prop price: number = 0;\\n  @Prop count: number = 0;\\n  @Prop productNames: string[] = [];\\n  @Prop productImages: Resource[] = [];\\n  @Consume(\'appPathStack\') appPathStack: NavPathStack;\\n\\n  jump(): void {\\n    this.appPathStack.pushPathByName(\'EmptyPagePathStack\', null);\\n  }\\n\\n  build() {\\n    Column() {\\n      Row() {\\n        Row() {\\n          Text(this.storeName)\\n            .fontColor($r(\'app.color.font_secondary\'))\\n            .fontSize($r(\'app.integer.subtitle_text_M\'))\\n            .fontWeight(FontWeight.Medium)\\n            .maxLines(1)\\n            .textOverflow({ overflow: TextOverflow.Ellipsis })\\n            .width(\'60%\')\\n        }\\n\\n        Row() {\\n          Text(this.storeTag)\\n            .fontColor($r(\'app.color.comp_background_emphasize\'))\\n            .fontSize($r(\'app.integer.body_text_S\'))\\n            .fontWeight(\'Regular\')\\n            .padding({ left:4,right:4,top:2,bottom:2 })\\n            .borderColor($r(\'app.color.comp_background_emphasize\'))\\n            .borderWidth(1)\\n            .borderRadius($r(\'sys.float.corner_radius_level4\'))\\n            .margin({right:8})\\n          Text(\'已完成\')\\n            .fontColor($r(\'app.color.font_tertiary\'))\\n            .fontSize($r(\'app.integer.body_text_S\'))\\n            .fontWeight(\'Regular\')\\n        }\\n      }\\n      .width(\'100%\')\\n      .justifyContent(FlexAlign.SpaceBetween)\\n      .alignItems(VerticalAlign.Center)\\n      .padding({ top: 4, bottom: 16 })\\n\\n      Row() {\\n        Row() {\\n          if (this.productImages.length > 0) {\\n            Image(this.productImages[0])\\n              .width(64)\\n              .height(64)\\n              .borderRadius(8)\\n              .margin({ right: 12 })\\n              .onClick(() => this.jump())\\n          }\\n          if (this.productImages.length > 1) {\\n            Image(this.productImages[1])\\n              .width(64)\\n              .height(64)\\n              .borderRadius(8)\\n              .onClick(() => this.jump())\\n          }\\n        }\\n        .alignItems(VerticalAlign.Center)\\n\\n        Column() {\\n          Text(`¥${this.price.toFixed(1)}`)\\n            .fontSize($r(\'app.integer.subtitle_text_L\'))\\n            .fontWeight(FontWeight.Bold)\\n            .fontColor($r(\'app.color.font_primary\'))\\n\\n          Text(`共${this.count}件`)\\n            .fontSize($r(\'app.integer.body_text_M\'))\\n            .fontWeight(FontWeight.Regular)\\n            .fontColor($r(\'app.color.font_secondary\'))\\n        }\\n        .alignItems(HorizontalAlign.End)\\n        .justifyContent(FlexAlign.Center)\\n      }\\n      .width(\'100%\')\\n      .justifyContent(FlexAlign.SpaceBetween)\\n      .margin({ bottom: 12 })\\n\\n      Row() {\\n        Button(\'开发票\')\\n          .controlSize(ControlSize.SMALL)\\n          .type(ButtonType.Capsule)\\n          .fontColor($r(\'app.color.font_on_primary\'))\\n          .backgroundColor($r(\'app.color.comp_background_emphasize\'))\\n          .margin({ right: 8 })\\n          .onClick(() => this.jump())\\n\\n        Button(\'再来一单\')\\n          .controlSize(ControlSize.SMALL)\\n          .type(ButtonType.Capsule)\\n          .fontColor($r(\'app.color.font_on_primary\'))\\n          .backgroundColor($r(\'app.color.comp_background_emphasize\'))\\n          .margin({ right: 8 })\\n          .onClick(() => this.jump())\\n      }\\n      .width(\'100%\')\\n      .justifyContent(FlexAlign.End)\\n    }\\n    .width(\'100%\')\\n    .backgroundColor(\'#FFFFFF\')\\n    .borderRadius(12)\\n    .padding(12)\\n    .margin({ bottom: 12 })\\n  }\\n}\\n\\n@Component\\nexport struct OrderListModule {\\n  @State dataModel: DataSource = DataSource.getInstance();\\n  @State loadingStatus: LoadingStatus = LoadingStatus.OFF;\\n  @State lazyDataSource: LazyDataSource<DataSourceStyleAStyleA> = this.dataModel.lazyDataSource;\\n  @Consume(\'appPathStack\') appPathStack: NavPathStack;\\n\\n  aboutToAppear() {\\n    this.loadResources();\\n  }\\n\\n  loadResources(): void {\\n    this.loadingStatus = LoadingStatus.LOADING;\\n    this.dataModel.getResources().then(() => {\\n      this.loadingStatus = LoadingStatus.SUCCESS;\\n    }).catch(() => {\\n      this.loadingStatus = LoadingStatus.FAILED;\\n    });\\n  }\\n\\n  build() {\\n    Column() {\\n      if (this.loadingStatus === LoadingStatus.FAILED) {\\n        LoadingFailedView(() => this.loadResources())\\n      }\\n\\n      if (this.loadingStatus === LoadingStatus.SUCCESS) {\\n        Column() {\\n          LazyForEach(this.lazyDataSource, (item: DataSourceStyleAStyleA) => {\\n            OrderItemCard({\\n              storeName: item.getStoreName(),\\n              storeTag: item.getStoreTag(),\\n              price: item.getPrice(),\\n              count: item.getCount(),\\n              productNames: item.getProductNames(),\\n              productImages: item.getProductImages()\\n            })\\n          }, (item: DataSourceStyleAStyleA, index: number) => index.toString() + item.getStoreName())\\n        }\\n        .width(\'100%\')\\n        .padding(16)\\n        .backgroundColor(\'#F5F5F5\')\\n      }\\n    }\\n    .width(\'100%\')\\n  }\\n", "error": null}', 'tool_call_id': 'view_file:5'}, {'role': 'assistant', 'content': '现在让我看看是否有路由配置，了解页面导航是如何设置的：', 'tool_calls': [{'index': 0, 'id': 'view_file:6', 'type': 'function', 'function': {'name': 'view_file', 'arguments': '{"absolute_path": "C:\\\\Users\\\\<USER>\\\\DevEcoStudioProjects\\\\MyApplication3\\\\entry\\\\src\\\\main\\\\ets\\\\pages\\\\Index.ets", "instruction": "查看主页面路由配置"}'}}]}, {'role': 'tool', 'content': '{"content": null, "error": "Root path C:\\\\Users\\\\<USER>\\\\DevEcoStudioProjects\\\\MyApplication3\\\\entry\\\\src\\\\main\\\\ets\\\\pages\\\\Index.ets does not exist"}', 'tool_call_id': 'view_file:6'}, {'role': 'assistant', 'content': '让我找找正确的入口文件：', 'tool_calls': [{'index': 0, 'id': 'find_path_by_name_pattern:7', 'type': 'function', 'function': {'name': 'find_path_by_name_pattern', 'arguments': '{"root_directory": "C:\\\\Users\\\\<USER>\\\\DevEcoStudioProjects\\\\MyApplication3\\\\entry\\\\src\\\\main\\\\ets", "pattern": "Index.ets", "instruction": "查找入口文件"}'}}]}, {'role': 'tool', 'content': '{"content": "[\\"Index.ets\\"]", "error": null}', 'tool_call_id': 'find_path_by_name_pattern:7'}, {'role': 'user', 'content': '请帮我实现以下请求：\n<请求>\n实现点击历史订单页面的历史订单后跳转到订单详情页面的能力。我不想看代码，这个工程是由一个页面生成的agent实现的，我也不知道实现了哪些代码，我只能看到应用的显示结果。。工程目录在 C:\\Users\\<USER>\\DevEcoStudioProjects\\MyApplication3\n</请求>\n<要求>\n1-我喜欢做事有条理的人，请使用通过拆分任务（任务管理）的方式来调理你的步调；先列计划，再执行，再在执行中调整计划；\n2-我想要参与进来，重要的决策要请示我，关键环节要等我批准；\n3-请帮我写优秀的代码，多分析下代码架构，多翻阅下资料。\n</要求>\n'}, {'role': 'assistant', 'content': '', 'tool_calls': [{'index': 0, 'id': 'view_file:8', 'type': 'function', 'function': {'name': 'view_file', 'arguments': '{"absolute_path": "C:\\\\Users\\\\<USER>\\\\DevEcoStudioProjects\\\\MyApplication3\\\\entry\\\\src\\\\main\\\\ets\\\\Index.ets", "instruction": "查看主入口文件，了解路由配置"}'}}]}, {'role': 'tool', 'content': '{"content": "import { EmptyPagePathStack } from \'./view/EmptyPagePathStack\';\\nimport { SecondPageSearch } from \'./view/SecondPageSearch\';\\nimport { BreakpointConstants } from \'./common/BreakpointConstants\';\\nimport { CommonConstants } from \'./common/CommonConstants\';\\nimport { HomePage } from \'./pages/HomePage\';\\nimport { OrderHistoryPage } from \'./pages/OrderHistoryPage\';\\nimport { OrderPage } from \'./pages/OrderPage\';\\nimport { ProductPage } from \'./pages/ProductPage\';\\n\\n@Entry\\n@Component\\nstruct Index {\\n  @State currentIndex: number = 0;\\n  private tabsController: TabsController = new TabsController();\\n  @Provide(\'appPathStack\') appPathStack: NavPathStack = new NavPathStack();\\n  @StorageLink(\'currentWidthBreakpoint\') currentWidthBreakpoint: string = BreakpointConstants.BREAKPOINT_SM;\\n\\n  @Builder\\n  PageMap(name: string) {\\n    if (name === \'SearchPage\') {\\n      SecondPageSearch()\\n    } else {\\n      EmptyPagePathStack()\\n    }\\n  }\\n\\n  @Builder\\n  tabBuilder(title: string, targetIndex: number, icon: Resource) {\\n    Column() {\\n      Image(icon)\\n        .width(\\"24vp\\")\\n        .height(\\"24vp\\")\\n        .objectFit(ImageFit.Contain)\\n        .fillColor(this.currentIndex === targetIndex ? ($r(\'app.color.app_icon_pressed_color\')) : ($r(\'app.color.app_icon_color\')))\\n      Text(title)\\n        .fontSize(\\"10vp\\")\\n        .fontColor(this.currentIndex === targetIndex ? ($r(\'app.color.app_icon_pressed_color\')) : ($r(\'app.color.app_icon_color\')))\\n        .textAlign(TextAlign.Center)\\n        .lineHeight(\\"14vp\\")\\n        .fontWeight(500)\\n    }\\n    .height(this.currentWidthBreakpoint === BreakpointConstants.BREAKPOINT_LG ? \\"100vp\\" : CommonConstants.FULL_PERCENT)\\n    .width(\'100%\')\\n    .justifyContent(FlexAlign.Center)\\n    .alignItems(HorizontalAlign.Center)\\n    .onClick(() => {\\n      this.currentIndex = targetIndex;\\n      this.tabsController.changeIndex(targetIndex);\\n    })\\n  }\\n\\n  build() {\\n    Navigation(this.appPathStack) {\\n      Tabs({\\n        barPosition: this.currentWidthBreakpoint === BreakpointConstants.BREAKPOINT_LG ? BarPosition.Start : BarPosition.End,\\n        controller: this.tabsController\\n      }) {\\n        TabContent() {\\n          HomePage()\\n        }.tabBar(this.tabBuilder(\'首页\', 0, $r(\\"app.media.lib_icon_8\\")))\\n        TabContent() {\\n          OrderHistoryPage()\\n        }.tabBar(this.tabBuilder(\'订单\', 1, $r(\\"app.media.lib_icon_173\\")))\\n        TabContent() {\\n          OrderPage()\\n        }.tabBar(this.tabBuilder(\'点单\', 2, $r(\\"app.media.lib_icon_173\\")))\\n        TabContent() {\\n          ProductPage()\\n        }.tabBar(this.tabBuilder(\'商品\', 3, $r(\\"app.media.lib_icon_50\\")))\\n      }\\n      .barWidth(this.currentWidthBreakpoint === BreakpointConstants.BREAKPOINT_LG ? \\"96vp\\" : CommonConstants.FULL_PERCENT)\\n      .barHeight(this.currentWidthBreakpoint === BreakpointConstants.BREAKPOINT_LG ? CommonConstants.FULL_PERCENT : \\"76vp\\")\\n      .barMode(this.currentWidthBreakpoint === BreakpointConstants.BREAKPOINT_LG ? BarMode.Scrollable : BarMode.Fixed, { nonScrollableLayoutStyle: LayoutStyle.ALWAYS_CENTER })\\n      .vertical(this.currentWidthBreakpoint === BreakpointConstants.BREAKPOINT_LG)\\n      .scrollable(false)\\n      .width(\'100%\')\\n      .height(\'100%\')\\n    }\\n    .backgroundColor($r(\'app.color.page_bg_color\'))\\n    .navDestination(this.PageMap)\\n    .hideTitleBar(true)\\n    .mode(NavigationMode.Stack)\\n  }\\n", "error": null}', 'tool_call_id': 'view_file:8'}, {'role': 'assistant', 'content': '好的！让我重新整理一下思路，先制定一个清晰的任务计划，然后我们一起逐步完成～', 'tool_calls': [{'index': 0, 'id': 'view_file:8', 'type': 'function', 'function': {'name': 'view_file', 'arguments': '{"absolute_path": "C:\\\\Users\\\\<USER>\\\\DevEcoStudioProjects\\\\MyApplication3\\\\entry\\\\src\\\\main\\\\ets\\\\Index.ets", "instruction": "查看入口文件，了解路由配置"}'}}]}, {'role': 'assistant', 'content': '现在让我查阅一下鸿蒙的导航最佳实践，确保我们采用正确的方式：', 'tool_calls': [{'index': 0, 'id': 'search_harmony_knowledge:9', 'type': 'function', 'function': {'name': 'search_harmony_knowledge', 'arguments': '{"query": "ArkTS Navigation页面跳转传参最佳实践", "instruction": "了解ArkTS中页面跳转和参数传递的最佳实践"}'}}]}, {'role': 'tool', 'content': '{"content": "import { EmptyPagePathStack } from \'./view/EmptyPagePathStack\';\\nimport { SecondPageSearch } from \'./view/SecondPageSearch\';\\nimport { BreakpointConstants } from \'./common/BreakpointConstants\';\\nimport { CommonConstants } from \'./common/CommonConstants\';\\nimport { HomePage } from \'./pages/HomePage\';\\nimport { OrderHistoryPage } from \'./pages/OrderHistoryPage\';\\nimport { OrderPage } from \'./pages/OrderPage\';\\nimport { ProductPage } from \'./pages/ProductPage\';\\n\\n@Entry\\n@Component\\nstruct Index {\\n  @State currentIndex: number = 0;\\n  private tabsController: TabsController = new TabsController();\\n  @Provide(\'appPathStack\') appPathStack: NavPathStack = new NavPathStack();\\n  @StorageLink(\'currentWidthBreakpoint\') currentWidthBreakpoint: string = BreakpointConstants.BREAKPOINT_SM;\\n\\n  @Builder\\n  PageMap(name: string) {\\n    if (name === \'SearchPage\') {\\n      SecondPageSearch()\\n    } else {\\n      EmptyPagePathStack()\\n    }\\n  }\\n\\n  @Builder\\n  tabBuilder(title: string, targetIndex: number, icon: Resource) {\\n    Column() {\\n      Image(icon)\\n        .width(\\"24vp\\")\\n        .height(\\"24vp\\")\\n        .objectFit(ImageFit.Contain)\\n        .fillColor(this.currentIndex === targetIndex ? ($r(\'app.color.app_icon_pressed_color\')) : ($r(\'app.color.app_icon_color\')))\\n      Text(title)\\n        .fontSize(\\"10vp\\")\\n        .fontColor(this.currentIndex === targetIndex ? ($r(\'app.color.app_icon_pressed_color\')) : ($r(\'app.color.app_icon_color\')))\\n        .textAlign(TextAlign.Center)\\n        .lineHeight(\\"14vp\\")\\n        .fontWeight(500)\\n    }\\n    .height(this.currentWidthBreakpoint === BreakpointConstants.BREAKPOINT_LG ? \\"100vp\\" : CommonConstants.FULL_PERCENT)\\n    .width(\'100%\')\\n    .justifyContent(FlexAlign.Center)\\n    .alignItems(HorizontalAlign.Center)\\n    .onClick(() => {\\n      this.currentIndex = targetIndex;\\n      this.tabsController.changeIndex(targetIndex);\\n    })\\n  }\\n\\n  build() {\\n    Navigation(this.appPathStack) {\\n      Tabs({\\n        barPosition: this.currentWidthBreakpoint === BreakpointConstants.BREAKPOINT_LG ? BarPosition.Start : BarPosition.End,\\n        controller: this.tabsController\\n      }) {\\n        TabContent() {\\n          HomePage()\\n        }.tabBar(this.tabBuilder(\'首页\', 0, $r(\\"app.media.lib_icon_8\\")))\\n        TabContent() {\\n          OrderHistoryPage()\\n        }.tabBar(this.tabBuilder(\'订单\', 1, $r(\\"app.media.lib_icon_173\\")))\\n        TabContent() {\\n          OrderPage()\\n        }.tabBar(this.tabBuilder(\'点单\', 2, $r(\\"app.media.lib_icon_173\\")))\\n        TabContent() {\\n          ProductPage()\\n        }.tabBar(this.tabBuilder(\'商品\', 3, $r(\\"app.media.lib_icon_50\\")))\\n      }\\n      .barWidth(this.currentWidthBreakpoint === BreakpointConstants.BREAKPOINT_LG ? \\"96vp\\" : CommonConstants.FULL_PERCENT)\\n      .barHeight(this.currentWidthBreakpoint === BreakpointConstants.BREAKPOINT_LG ? CommonConstants.FULL_PERCENT : \\"76vp\\")\\n      .barMode(this.currentWidthBreakpoint === BreakpointConstants.BREAKPOINT_LG ? BarMode.Scrollable : BarMode.Fixed, { nonScrollableLayoutStyle: LayoutStyle.ALWAYS_CENTER })\\n      .vertical(this.currentWidthBreakpoint === BreakpointConstants.BREAKPOINT_LG)\\n      .scrollable(false)\\n      .width(\'100%\')\\n      .height(\'100%\')\\n    }\\n    .backgroundColor($r(\'app.color.page_bg_color\'))\\n    .navDestination(this.PageMap)\\n    .hideTitleBar(true)\\n    .mode(NavigationMode.Stack)\\n  }\\n", "error": null}', 'tool_call_id': 'view_file:8'}]

    hippocampus = Hippocampus()
    hippocampus.memory.memory = memory
    hippocampus.original_memory.memory = memory
    hippocampus.get_original_memory().export_to_file("mem_test_export.txt")
    asyncio.run(hippocampus.memory.compress_memory())

    logger.debug(hippocampus.get_complete_memory())
