import asyncio

from pydantic import BaseModel

from backend.src.brain.hippocampus.hippocampus import Memory
from backend.src.brain.id.orchestrator import Orchestrator
from backend.src.core.logging import get_module_logger
from backend.src.schemas.session_data_schema import SessionData
from backend.src.services.tool_service import ToolService
from backend.src.session.session import Session
from backend.src.tools.base import BaseExecTool, ToolResult

logger = get_module_logger(__name__)

class ExecuteTask(BaseModel):
    task_background: str
    task_target: str

class DistributeTaskTool(BaseExecTool):
    name: str = "distribute_task"
    description: str = """将任务分发给子agent执行。"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "task_background": {
                "type": "string",
                "description": """任务背景。应描述本任务的前置背景与知识信息。""",
            },
            "task_target": {
                "type": "string",
                "description": """任务完成目标。应描述包括目标、回复格式、回复信息等。""",
            },
            "test_strategy": {
                "type": "string",
                "description": """验证策略。需要明确如何验证任务完成。""",
            }
        },
        "required": ["task_background", "task_target", "test_strategy"],
    }
    session: Session = None
    tool_service: ToolService = None

    def __init__(self, session: Session):
        super().__init__()
        self.session = session

    def set_tool_service(self, tool_service: ToolService):
        self.tool_service = tool_service

    async def execute(self, **kwargs) -> ToolResult:
        """
        """
        task = ExecuteTask(**kwargs)

        prompt = f"""你是代码开发辅助agent群组中的一个子agent，由上层agent向你分发任务。请确保你将最有效信息回复给上层agent。不要做分外的事。不要询问下一步该做什么，不要询问是否确定，不要做任何询问。
注意：上层agent看不到你的过程细节，如果有已做过的重复动作，确认有效后可以不再执行。
请完成以下任务：【{task.task_target}】。
任务背景为：【{task.task_background}】"""

        logger.debug(f"分发任务：{prompt}")
        self.memory.add_user_msg(prompt)
        orchestrator = Orchestrator(self.session, self.tool_service)
        response = await orchestrator.run()
        return ToolResult(content=response)


if __name__ == "__main__":
    tool = DistributeTaskTool(Session(SessionData(**{
        "device_id": "111",
        "session_id": "222",
        "interaction_id": "333"
    })))
    logger.info(asyncio.run(tool.execute(**{
        "task_background": "定位XX文件",
        "task_target": "定位XX功能的XX位置"
    })))
