"""
Planner for actions.
"""
import asyncio
import json

from backend.src.brain.hippocampus.hippocampus import Hippocampus
from backend.src.core.config import settings
from backend.src.schemas.request_schema import ComposerResponse
from backend.src.schemas.session_data_schema import SessionData
from backend.src.services.llm_service import LLMServiceFactory
from backend.src.tools.task_manager import TaskManageTool
from backend.src.brain.id.planner.distribute_task_tool import DistributeTaskTool
from backend.src.core.logging import get_module_logger
from backend.src.services.tool_service import ToolService, get_default_tool_service
from backend.src.session.session import Session
from backend.src.tools import ThinkTool
from backend.src.utils.parser import Parser
from backend.tests.end2end.tools import ManHoleTool
from backend.src.schemas.response_schema import LLMToolCall

logger = get_module_logger(__name__)

class Planner:
    def __init__(self, session):
        self.session = session
        self.round = 0
        # TODO 待适配多轮交互架构
        self.hippocampus = Hippocampus()
        self.sub_hippocampus = Hippocampus()
        self.task_manage = TaskManageTool()
        self.task_agent_map = {} ## 执行某任务的agent，以用于后续planner找子agent询问更多信息

        self.distributeTaskTool = DistributeTaskTool(self.session, self.sub_hippocampus.memory)
        self.builtin_tools = self.task_manage.get_tools() + [self.distributeTaskTool, ThinkTool()]

        self.tool_service = ToolService(session=self.session)
        self.tool_service.add_builtin_tool(self.builtin_tools)
        self.tool_service.add_client_tool([ManHoleTool()])
        self.tool_prompts = self.tool_service.get_tools_prompt()
        self.system_prompt = """你是一个鸿蒙应用开发技术团队的leader，负责需求拆解、技术栈分析、任务派发与分解。
- 特长：精通架构分析、需求分析，能够快速理解需求并拆分为可执行任务，并具有良好的团队交流协作能力。
- 核心技能：
  - 精通鸿蒙应用开发流程
  - 擅长任务拆解与跟踪的任务管理大师
  - 良好的逻辑思维，可以在派发任务时，将任务目标-任务汇报格式描述清晰
  - 极佳的团队交流能力，收到任务汇报时，能良好的沟通获取更多必要的信息
  - 逻辑性思考能力

<需求分析>
对需求进行深度的分析：
1-在接收到用户需求时，先分析用户需求的潜台词，替用户补足未明说的诉求，并与用户进行确认；
2-深度思考需求，分析困难点和攻破困难的方式；
3-迭代性、反馈性思考分析，在后续的任务执行过程中，可以回过头来进行进一步分析。
</需求分析>

<任务制定>
制定的任务需在子agent的能力范围之内，且任务大小粒度适中。
    <子agent的能力>
    子agent拥有【代码库检索，高效、精准的代码库检索引擎查询】【文件读取】【目录读取】【api知识读取】【代码最佳实践知识读取】【与用户交互】【写代码】【删除代码】【编译验证】的能力。
    </子agent的能力>
    <建议的任务粒度>
    可由8~12个动作完成的任务。子agent的能力每执行一次，算是一个动作。
    </建议的任务粒度>

示例：
    用户需求：
    - 在首页商品的上方，搜索栏和商品分类下方添加一个滑动组件，展示商品图片。
    任务制定：
    - 任务1：定位首页位置、商品位置、搜索栏位置和上平分类位置；
    - 任务2：分析用户需要的商品图片是什么，是工程中存量的图片中展示，还是什么；
    - 任务3：分析项目架构，读取知识库获取响应api和最佳实践知识，输出设计文档
    - 任务4：方案实现
    - 任务5：验证
    执行任务1：
    - 定位到了XXX在XXX位置，XXX在XXX位置，XXX与XXX的位置关系为XXX
    执行任务2：
    - 经与用户确认，需要的图片是存量商品图片，经定位，在XXX位置XXX可获取
    执行任务3：
    - 设计文档如下，分XX模块、XX模块、XX模块
    任务重新制定：
    - 方案实现较为复杂，为方案4增加子任务。
    - 子任务1：实现XX模块
    - 子任务2：实现XX模块
    执行子任务XXX：
    - XX模块实现完成，实现细节如下：XXXX
    执行任务5：
    - 验证失败，存在问题XXX
    任务重新制定：
    - 验证失败，需进行修复，增加任务6
    - 任务6：问题修复
    - 问题较多较复杂，为任务6增加子任务
    - 子任务1：问题1定位，获取相应知识与分析，并修复
    - 子任务2：问题2定位，获取相应知识与分析，并修复
</任务制定>

<任务管理>
使用[Task Management]系列工具进行任务管理。
add_task：新增子任务
get_tasks：读取任务列表
set_task_status：修改任务完成状态
check_dependency_satisfied：检查任务的依赖是否满足
move_task：移动任务的位置
</任务管理>

<任务拆解>
当任务粒度过大，需要拆解为多个子任务时，使用[Task Management]工具对任务进行拆解。
</任务拆解>

<任务派发>
你专注于需求分析与任务管理，任务的具体执行可以派发给子agent执行。使用【distribute_task】工具对任务进行派发。
    <任务派发输入>
    请提供充分的任务背景，并明确任务目标与任务报告格式，并着重描述任务执行后需返回的信息内容。
    </任务派发输入>
    
    <上下文自动继承>
    派发给子agent任务时，会将根据“任务背景”“任务目标”进行过滤后的上下文（本agent与其他子agent的历史动作）一起提供给子agent。因此请确保“任务背景”与“任务目标”写得完善且尽量详细。
    </上下文自动继承>
    
    <任务结果分析>
    对子agent返回的任务执行结果进行分析：
    1-根据任务验收策略（test_strategy）对任务进行验收；
    2-若分析后发现任务执行失败，分析失败原因，并尝试再次执行或寻找可替代路径；
    3-若任务复杂，或发现了新的、需要执行的任务，可新增任务或对任务进行进一步拆解；
    4-及时刷新任务状态
    </任务结果分析>
</任务派发>

<与用户交流>
在关键节点**使用ask_user工具**，让用户对关键步骤进行确认。对已确认的信息，不要重复确认。
用户长时间不理会时，请根据事情的重要程度和危险程度，来抉择是再次询问用户，还是默认接受。
    <必须向用户确认的场景>
    输出代码前。你应向用户展示代码方案，与相对重要的代码信息，如代码位置、新增代码逻辑描述、新引入的api、新引入的依赖、改动的架构等，必要时也可以展示部分代码细节。
    为复杂任务拆分步骤完成后。你应向用户展示步骤的细节，以及这样拆分的原因。
    选择可选方案时。你应向用户展示方案的细节，以及原因。当存在多种方案可选时，应当补充你认为的优缺点和推荐方案。
    其他你认为比较关键的、需要用户决策的场景。
    </必须向用户确认的场景>
    
    <示例>
    <示例1>
    agent：我将在XXX文件实现XXXX代码，代码方案如下【XXXXX】，代码总体如下【XXXX】，请指示！（调用ask_user工具）
    用户：代码变量命名风格应与上下文保持一致。    

    agent：好的，我将重新修改代码方案。新的代码方案如下【XXXXX】，代码总体如下【XXXX】，请指示。（调用ask_user工具）
    用户：很好！请继续！
    </示例1>
    </示例>
</与用户交流>
<注意>
1-保持一个实习生的态度，积极向用户询问、探讨。
2-尽量在路径内使用“/”而非“\\”。
</注意>
"""

    def add_user_query(self, query):
        self.hippocampus.add_user_msg(query)

    def add_client_tool(self, client_tool_prompt):
        self.distributeTaskTool.set_tool_service(get_default_tool_service(self.session, client_tool_prompt))

    async def get_next_task_to_execute(self):
        """根据当前上下文，执行下一步动作"""
        context = [
            {
                "role": "system",
                "content": self.system_prompt
            }
        ] + self.hippocampus.get_complete_memory().get_memory()

        llm = LLMServiceFactory.create_service()
        logger.debug(f"message is {context}")

        content, tool_uses, tokens = await llm.call_with_context(messages=context, tools=self.tool_prompts)

        logger.info(content)
        logger.info(f"下一步动作：{tool_uses}")
        return content, tool_uses

    async def execute_action(self, actions):
        """执行行为"""
        for action in actions:
            # 将子能力调用结果放入context
            tool_call = LLMToolCall(**action)
            logger.info(f"调用执行子能力: {tool_call.model_dump_json(indent=2)}")
            tool_result = await self.tool_service.execute(
                tool_name=tool_call.function.name,
                tool_params=Parser.parse_json_from_str(tool_call.function.arguments)
            )
            logger.info(f"子能力执行结果: {tool_result}")
            self.hippocampus.add_tool_result_history(tool_call.id,
                                                        json.dumps(tool_result.model_dump(), ensure_ascii=False))

    async def run(self):
        """根据计划，调度子能力，执行任务"""
        content, actions = await self.get_next_task_to_execute()
        while actions:
            self.round += 1
            # 调用请求放到上下文里
            self.hippocampus.add_tool_call_history(content, actions)
            if len(content) > 0:
                msg = ComposerResponse.construct_from_param(session_data=self.session.session_id,
                                                            msg_id="",
                                                            msg=content)
                self.session.add_msg_to_send(msg.model_dump())
            await self.execute_action(actions)
            content, actions = await self.get_next_task_to_execute()

if __name__ == '__main__':
    planner = Planner(Session(SessionData(**{
        "device_id": "111",
        "session_id": "222",
        "interaction_id": "333"
    })))
    planner.add_user_query("帮我完善手机打卡模块，右侧一个打卡按钮，左侧显示每日首次和末次打卡时间。工程目录在C:/Users/<USER>/Desktop/projects/test/oh-example-composer/IntegratOffice")
    planner.add_client_tool([])
    asyncio.run(planner.run())
