"""
进行流程调度与按计划的执行
"""
import json
import time

from pydantic import ValidationError

from backend.src.core.logging import get_module_logger, trace_var
from backend.src.llm.prompts import PromptTemplates
from backend.src.schemas.metrics_schema import StepType
from backend.src.schemas.response_schema import LLMToolCall
from backend.src.services.llm_service import LLMServiceFactory
from backend.src.services.tool_service import ToolService
from backend.src.session.session import Session

from backend.src.utils.wrapper import async_time_mark


logger = get_module_logger(__name__)


class Orchestrator:
    def __init__(self, session: Session, tool_service: ToolService):
        self.session = session
        self.hippocampus = self.session.hippocampus
        self.tool_manager = None
        self.prompt_manager = PromptTemplates()
        self.tool_service = tool_service
        self.round = 0
        trace_var.set(str(session.get_session_name()))

    @async_time_mark
    async def get_next_action(self):
        """根据当前上下文，执行下一步动作"""
        system_prompt = self.prompt_manager.get_system_prompt()
        tools = self.tool_service.get_tools_prompt()

        context = [
            {
                "role": "system",
                "content": system_prompt
            },
            {
                "role": "user",
                "content": f"""This is your dynamically updated task list. All operations performed through the [Task Management] functions will be synchronized here in real time.
# Task List
{(await self.tool_service.execute(tool_name="get_tasks", tool_params={})).content}
"""
            }
        ] + self.hippocampus.get_complete_memory().get_memory()

        llm = LLMServiceFactory.create_service()

        logger.debug(f"message is {context}")
        content, tool_uses, tokens = await llm.call_with_context(messages=context, tools=tools)

        logger.info(content)
        logger.info(f"下一步动作：{tool_uses}")
        return content, tool_uses, tokens

    async def execute_action(self, actions):
        """执行行为"""
        for action in actions:
            # 将子能力调用结果放入context
            tool_call = LLMToolCall(**action)
            logger.info(f"调用执行子能力: {tool_call.model_dump_json(indent=2)}")
            self.session.add_response_msg_to_send(f"调用执行子能力: {tool_call.model_dump_json(indent=2)}")
            tool_result = await self.tool_service.execute(
                tool_name=tool_call.function.name,
                tool_params=tool_call.function.arguments
            )
            time_usage = self.tool_service.execute.time_usage
            self.session.recorder.record(step_type=StepType.EXECUTION, time_usage=time_usage,
                            input_data=tool_call.model_dump(), output_data=tool_result.model_dump())
            logger.info(f"子能力执行结果: {tool_result}")
            self.hippocampus.add_tool_result_history(tool_call.id,
                                                     json.dumps(tool_result.model_dump(), ensure_ascii=False))

    async def run(self):
        """根据计划，调度子能力，执行任务"""
        actions, content = await self.think(self.session.supplement_info if self.session.supplement_info else self.session.user_query)
        while actions and self.session.get_running_state():
            self.round += 1
            # 调用请求放到上下文里
            self.hippocampus.add_tool_call_history(content, actions)
            if len(content) > 0:
                self.session.add_response_msg_to_send(content)
            try:
                await self.execute_action(actions)
            except ValidationError as e:
                try_again_content = f"Parse tool arguments string to json failed with error {e}, please try generate tool arguments again."
                self.hippocampus.add_user_msg(try_again_content)
            except Exception as e:
                logger.error(f"Error when execute action: {e}")
            await self.hippocampus.get_complete_memory().compress_memory()
            actions, content = await self.think()
        if self.session.get_running_state():
            self.hippocampus.add_content_history(content)
            self.session.recorder.set_end_time(time.time())
            self.hippocampus.original_memory.export_to_file(f"{self.session.get_session_name()}.json")
            self.session.recorder.save_to_csv(f"{self.session.get_session_name()}.csv")
            if len(content) > 0:
                self.session.add_response_msg_to_send(content)
            self.session.add_response_msg_to_send("##COMPOSER##FEEDBACK_REQUIRED##")
        return content

    async def think(self, user_query=None):
        content, actions, tokens = await self.get_next_action()
        time_usage = self.get_next_action.time_usage
        if not user_query:
            self.session.recorder.record(step_type=StepType.PLANNING, output_data={"content": content, "actions": actions},
                            time_usage=time_usage, token_usage=tokens)
        else:
            self.session.recorder.record(step_type=StepType.USER_REQUEST, input_data=user_query,
                            output_data={"content": content, "actions": actions},
                            time_usage=time_usage, token_usage=tokens)

        return actions, content
