"""
The ego of a brain.
自我，在社会生活中表现出追求各种个人欲望的满足和追求个人利益实现的特征。一方面调节着本我，一方面又受制于超我。
"""
from backend.src.brain.id.orchestrator import Orchestrator
from backend.src.services.tool_service import ToolService


class QueryContext:
    def __init__(self, user_query):
        self.user_query = user_query


class Ego:
    def __init__(self, session, context: QueryContext, tool_service: ToolService):
        self.session = session
        self.context = context
        self.tool_service = tool_service
        self.memory = self.session.hippocampus.get_goal_related_memory(self.context.user_query)
        self.orchestrator = Orchestrator(self.session, self.tool_service)

    async def run(self):
        await self.orchestrator.run()

    async def stop(self):
        self.orchestrator.stop()

    async def go_on(self):
        # 预留，后续goon与run应有差异
        await self.orchestrator.run()
