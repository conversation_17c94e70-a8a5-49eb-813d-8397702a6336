import asyncio
from typing import Any, List, Optional, Dict, Callable, Tuple, Union

from backend.src.core.logging import get_module_logger
from backend.src.llm.base import BaseLLMService

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)


class FastRouterService(BaseLLMService):
    """FastRouterAPI服务实现类"""

    def __init__(
            self,
            api_key: str,
            model_name: str,
            temperature: float = 0.3,
            top_p: float = 0.8,
            top_k: float = 1,
            max_tokens: int = 4096,
            request_timeout: float = 60.0,
            max_retries: int = 3,
            retry_delay: float = 1.0
    ):
        super().__init__()
        self.api_key = api_key
        self.api_endpoint = "https://api-key.info/v1/messages"
        self.model_name = model_name
        self.temperature = temperature
        self.top_p = top_p
        self.top_k = top_k
        self.max_tokens = max_tokens
        self.request_timeout = request_timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def _parse_response(self, response: Dict[str, Any]) -> Tuple[str, List[Dict[str, Any]], Dict[str, Any]]:
        """解析特定模型的响应(需要子类实现)"""
        resp = ''
        tools = []
        for cont in response.get("content", []):
            if cont.get("type") == "text":
                resp = cont.get("text")
            elif cont.get("type") == "tool_use":
                tools.append(cont)

        return resp, tools, response.get("usage", {})


if __name__ == "__main__":
    LLMService = FastRouterService(api_key='sk-',
                                    model_name='claude-sonnet-4-20250514')
    output = asyncio.run(LLMService.call_with_context(
            messages=[{"role": "user", "content": "查看今天杭州和上海的天气如何，并查询从杭州到上海的火车票价格"}],
            tools=[{'name': 'get_weather', 'description': 'Get the current weather for a given city.', 'input_schema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': 'The name of the city to query weather for.'}}, 'required': ['city']}}, {'name': 'get_ticket_price', 'description': 'Get the train ticket price between two cities.', 'input_schema': {'type': 'object', 'properties': {'starting_city': {'type': 'string', 'description': 'Departure city name for the train.'}, 'destination_city': {'type': 'string', 'description': 'Arrival city name for the train.'}}, 'required': ['starting_city', 'destination_city']}}]))
    print(output)
