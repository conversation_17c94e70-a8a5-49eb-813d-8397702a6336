import asyncio

from backend.src.core.logging import get_module_logger
from backend.src.llm.base import BaseLLMService

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)


class AliyuncsService(BaseLLMService):
    """阿里云百炼API服务实现类"""

    def __init__(
            self,
            api_key: str,
            model_name: str,
            temperature: float = 0.3,
            top_p: float = 0.8,
            top_k: float = 1,
            max_tokens: int = 4096,
            request_timeout: float = 60.0,
            max_retries: int = 3,
            retry_delay: float = 1.0
    ):
        super().__init__()
        self.api_key = api_key
        self.api_endpoint = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        self.model_name = model_name
        self.temperature = temperature
        self.top_p = top_p
        self.top_k = top_k
        self.max_tokens = max_tokens
        self.request_timeout = request_timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay


if __name__ == "__main__":
    LLMService = AliyuncsService(api_key='sk-',
                                    model_name='Moonshot-Kimi-K2-Instruct')
    output = asyncio.run(LLMService.call("你好"))
    print(output)
