import json
import aiohttp
import asyncio
import time
import logging
import numpy as np
from typing import AsyncIterator, Dict, List, Optional, Tuple, Union, Any
from ..core.logging import get_module_logger
from ..core.config import settings
from .base import BaseLLMService


# 初始化模块级logger
logger = get_module_logger(__name__)

class OllamaService(BaseLLMService):
    """Ollama服务实现类
    
    提供与Ollama模型交互的具体实现，包括：
    - 模型参数配置
    - 流式输出处理
    - 响应一致性处理
    - 错误重试机制
    """
    
    def __init__(
        self,
        model: str = "qwen:7b",
        base_url: str = "http://localhost:11434",
        temperature: float = 0.3,
        top_p: float = 0.8,
        request_timeout: float = 60.0,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """初始化Ollama服务
        
        Args:
            model: 模型名称
            base_url: Ollama服务地址
            temperature: 温度参数，控制随机性，较低的值会使输出更加确定性
            top_p: 核采样参数
            request_timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            consistency_mode: 是否启用一致性模式，启用后会添加额外的一致性提示
        """
        self.model = model
        self.base_url = base_url.rstrip('/')
        self.temperature = temperature
        self.top_p = top_p
        self.request_timeout = request_timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        