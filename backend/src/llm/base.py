from abc import ABC, abstractmethod
from typing import Any, List, Optional, Dict, Callable, Tuple, Union
import requests
import time

class BaseLLMService(ABC):
    """LLM服务的基础接口类，定义了与语言模型交互的标准方法"""

    def __init__(self):
        """
        API模型基类

        参数:
            request_timeout: 请求超时时间(秒)
            max_retries: 最大重试次数
            retry_delay: 重试间隔时间(秒)
        """
        self.api_endpoint = None
        self.api_key = None
        self.model_name = None
        self.temperature = None
        self.top_p = None
        self.max_tokens = None
        self.request_timeout = None
        self.max_retries = None
        self.retry_delay = None


    def _should_retry(self, status_code: int, response_data: Dict[str, Any]) -> bool:
        """
        判断是否需要重试请求
        默认处理429限流和5xx服务器错误
        """
        if status_code == 429 or (500 <= status_code < 600):
            return True

        # todo 模型特定的错误处理
        # if "error" in response_data:
        #     error_code = response_data["error"].get("code", "")
        #     if error_code in ["rate_limit_exceeded", "server_busy"]:
        #         return True

        return False

    def _create_headers(self) -> Dict[str, str]:
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }


    def _create_payload(self, prompt: str) -> Dict[str, Any]:
        return {
            "model": self.model_name,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "top_p": self.top_p,
        }


    def _create_context_payload(
            self,
            messages: List[Dict[str, str]],
            knowledge: Optional[List[Dict[str, str]]] = None,
            tools: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """通用模型带的请求体(特殊模型需要在各子类单独实现)"""
        if knowledge:
            # 将知识片段格式化为硅基流动要求的格式
            knowledge_context = [{"role": "system", "content": k["content"]} for k in knowledge]
            messages = knowledge_context + messages
        if tools:
            return {
                "model": self.model_name,
                "messages": messages,
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "top_p": self.top_p,
                "tools": tools,
                "parallel_tool_calls": True
            }

        return {
            "model": self.model_name,
            "messages": messages,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "top_p": self.top_p
        }


    def _parse_response(self, response: Dict[str, Any]) -> Tuple[str, List[Dict[str, Any]], Dict[str, Any]]:
        """解析特定模型的响应(需要子类实现)"""
        return response["choices"][0]["message"].get("content", '').strip(), response["choices"][0]["message"].get("tool_calls",[]), response.get('usage', {})

    async def call(self, prompt: str, stream: bool = False) -> tuple[Any, Any]:
        """根据提示词生成回答

        Args:
            api_point: 模型api url
            api_key: 模型api key
            prompt: 输入提示词
            stream: 是否使用流式输出

        Returns:
            完整回答文本或回答生成器
        """

        payload = self._create_payload(prompt)
        headers = self._create_headers()

        def request_wrapper():
            response = requests.post(
                headers= headers,
                url=self.api_endpoint,
                json=payload,
                timeout=self.request_timeout
            )
            response_data = response.json() if response.content else {}
            return response, response_data

        return self._retryable_request(request_wrapper, self._parse_response)[0]


    async def call_with_context(
        self,
        messages: List[Dict[str, str]],
        knowledge: Optional[List[Dict[str, str]]] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False
    ) -> tuple[Any, Any, Any]:
        """基于历史消息和知识生成回答
        
        Args:
            messages: 历史对话消息列表
            knowledge: 相关的知识片段列表
            stream: 是否使用流式输出
            tools: 工具列表

        Returns:
            完整回答文本或回答生成器
        """

        # 创建带上下文的请求体
        payload = self._create_context_payload(messages, knowledge, tools)
        headers = self._create_headers()

        def request_wrapper():
            response = requests.post(
                url=self.api_endpoint,
                headers=headers,
                json=payload,
                timeout=self.request_timeout
            )
            response_data = response.json() if response.content else {}
            return response, response_data

        return self._retryable_request(request_wrapper, self._parse_response)

    def _format_error(self, status_code: int, response_data: Dict[str, Any]) -> str:
        """格式化错误信息"""
        if "error" in response_data:
            error_info = response_data["error"]
            if isinstance(error_info, dict):
                return f"Status {status_code}: {error_info.get('code', '')} - {error_info.get('message', '')}"
            return f"Status {status_code}: {str(error_info)}"
        return f"Status {status_code}"

    def _retryable_request(
            self,
            request_func: Callable[[], Tuple[Any, Any]],
            parse_func: Callable[[Dict], Tuple[Any, Any, Any]]
    ) -> tuple[Any, Any, Any]:
        """
        通用的重试请求机制

        参数:
            request_func: 执行请求的函数，返回(response, response_data)
            parse_func: 解析响应的函数

        返回:
            解析后的响应内容
        """
        attempts = 0
        last_error = None

        while attempts <= self.max_retries:
            try:
                response, response_data = request_func()

                # 处理成功响应
                if response.status_code == 200:
                    return parse_func(response_data)

                # 检查是否需要重试
                if attempts < self.max_retries and self._should_retry(response.status_code, response_data):
                    # 计算回退时间（指数退避）
                    sleep_time = self.retry_delay * (2 ** attempts)
                    time.sleep(sleep_time)
                    attempts += 1
                    continue

                # 处理非重试错误
                error_detail = self._format_error(response.status_code, response_data)
                raise RuntimeError(f"API调用失败: {error_detail}")

            except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
                # 网络错误处理
                if attempts < self.max_retries:
                    sleep_time = self.retry_delay * (2 ** attempts)
                    time.sleep(sleep_time)
                    attempts += 1
                    last_error = str(e)
                    continue
                raise RuntimeError(f"网络请求超时或失败: {str(e)}")

            except Exception as e:
                # 其他错误直接抛出
                raise RuntimeError(f"API调用错误: {str(e)}")

        # 重试次数耗尽
        raise RuntimeError(f"超过最大重试次数({self.max_retries}): {last_error or '未知错误'}")

    async def get_embedding(self, text: str) -> List[float]:
        """获取文本的向量表示

        Args:
            text: 输入文本

        Returns:
            文本的向量表示（浮点数列表）
        """
        pass


    async def check_service_status(self) -> Dict[str, Union[bool, str, List[str]]]:
        """检查LLM服务状态

        Returns:
            服务状态信息，包含以下字段：
            - available: 服务是否可用
            - model_available: 当前配置的模型是否可用
            - models: 可用模型列表（如果服务支持）
            - message: 状态描述信息
        """
        pass