import argparse
import asyncio
import threading
import sys
import time
from pathlib import Path

from fastapi import FastAPI, WebSocket
from contextlib import asynccontextmanager

root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from backend.src.schemas.request_schema import Request, ToolUseRequest, SupplementRequest
from backend.src.schemas.response_schema import ToolUseResponse, StopResponse
from backend.src.services.code_generator_service import CodeGeneratorService
from backend.src.session.session import Session
from backend.src.session.session_manager import SessionManager
from backend.src.api.v1.router import router as api_v1_router
from backend.src.core.config_loader import load_configurations
from backend.src.core.logging import setup_logging, app_logger as logger, trace_var
from backend.src.core.config import settings
# 导入中间件
from backend.src.core.middleware import RequestLoggingMiddleware

def initialize_app():
    """初始化应用配置和日志"""
    load_configurations()
    setup_logging()
    logger.info("应用启动完成...")


app = FastAPI(
    title=settings.PROJECT_NAME,
    description="Backend for AI Programming Agent based on Composer architecture.",
    version="0.1.0",
    openapi_url=None
)

# 添加请求日志中间件
app.add_middleware(
    RequestLoggingMiddleware,
    exclude_paths=["/api/v1/health", "/metrics", "/favicon.ico"]
)

session_manager = SessionManager()


async def startup_event():
    initialize_app()


ml_models = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Load the ML model
    ml_models["answer_to_everything"] = startup_event
    yield
    # Clean up the ML models and release the resources
    ml_models.clear()


@app.get("/")
async def read_root():
    """
    Root endpoint for the API.
    """
    return {"message": f"Welcome to {settings.PROJECT_NAME}"}


def response_tool(session, msg):
    session.receive_one_msg(msg.data.msg_id, msg)

def stop(session):
    session.stop()

async def supplement_info(session, msg: SupplementRequest):
    session.executor.supplement_info(msg.data.msg.query)
    await session.executor.go_on()

def feedback(session, msg: SupplementRequest):
    session.executor.feedback(msg.data.msg.query)
    session.stop()

async def generate_code(session, msg):
    logger.info("receive a generate_code call")
    session.executor = CodeGeneratorService(session)
    await session.executor.execute(msg)


async def handle_req(session: Session, msg: ToolUseResponse | Request | StopResponse):
    """"""
    token = trace_var.set(str(session.get_session_name()))
    try:
        msg_type = msg.data.msg_type
        if msg_type == "query_agent":
            logger.info(f"收到用户请求, session: {msg.session}, \n需求: {msg.data}, \n工具列表: {msg.tools}")
            session.recorder.set_start_time(time.time())
            await generate_code(session, msg)
        elif msg_type == "tool_response":
            logger.debug(f"收到工具结果, session: {msg.session}, \n结果: {msg.data}")
            response_tool(session, msg)
        elif msg_type == "supplemented_info":
            logger.debug(f"收到用户的补充信息, session: {msg.session}, \n信息: {msg.data}")
            await supplement_info(session, msg)
        elif msg_type == "feedback":
            logger.debug(f"收到用户的反馈信息, session: {msg.session}, \n信息: {msg.data}")
            feedback(session, msg)
        elif msg_type == "stop":
            logger.debug(f"收到暂停信息")
            stop(session)
        elif msg_type == "terminate":
            logger.debug(f"收到终止信息")
            stop(session)
        else:
            logger.error(f"Unknown message type: {msg_type}")
    except Exception as e:
        logger.error(str(e), exc_info=True)
    finally:
        trace_var.reset(token)


class MyThread(threading.Thread):
    def __init__(self, msg, session: Session):
        super(MyThread, self).__init__()
        self.msg = msg
        self.session = session

    def run(self):
        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(handle_req(self.session, self.msg))
        except Exception as e:
            logger.error(f"Error when client execute tool: {e}")
            raise
        finally:
            # 关闭事件循环
            loop.close()


async def receive_msg(message, websocket: WebSocket, session: Session):
    """"""
    try:
        while True:
            try:
                t = MyThread(message, session)
                t.start()
            except Exception as e:
                logger.error(e)
            logger.info("waiting for message")
            received_msg = await websocket.receive_json()

            logger.debug(f"received_msg: {received_msg}")
            ## 后续需动态判断消息类型，或增加消息解析兼容性。当前固定写死为tool response。
            msg_type = received_msg.get("data", {}).get("msg_type", "")
            if msg_type == "tool_response":
                message = ToolUseResponse.construct_from_json(received_msg)
            elif msg_type == "stop" or msg_type == "terminate":
                message = StopResponse.construct_from_param(session.session_id)
            elif msg_type == "supplemented_info":
                message = SupplementRequest.construct_from_json(received_msg)
            elif msg_type == "feedback":
                message = SupplementRequest.construct_from_json(received_msg)
    except Exception as e:
        logger.error(e)


async def send_msg(websocket: WebSocket, session: Session):
    """"""
    try:
        while True:
            while session.check_msg_to_send():
                msg = session.get_one_msg_to_send()
                if msg:
                    await websocket.send_json(msg)
                await asyncio.sleep(1)
            await asyncio.sleep(0.5)
    except Exception as e:
        logger.error(e)


@app.websocket("/query_agent")
async def query_agent(websocket: WebSocket):
    await websocket.accept()
    websocket.websocket_timeout = 100
    logger.info(f"\n\n>>>>>> New client connected <<<<<<<")
    msg = Request.construct_from_json(await websocket.receive_json())
    session = session_manager.get_session(msg.session)
    trace_var.set(str(session.get_session_name()))
    try:
        await asyncio.gather(receive_msg(msg, websocket, session), send_msg(websocket, session))
    finally:
        session_manager.delete_session(msg.session)
        logger.info(f"session {session} deleted")


app.include_router(api_v1_router, prefix=settings.API_V1_STR)

# 启动命令
if __name__ == "__main__":
    import uvicorn
    setup_logging()

    parser = argparse.ArgumentParser(description="composer")
    parser.add_argument('--composer_port', type=str, default=settings.COMPOSER_PORT, help="Composer server port")
    args = parser.parse_args()

    uvicorn.run(app, host="0.0.0.0", port=int(args.composer_port), ws_ping_interval=10, ws_ping_timeout=None)
