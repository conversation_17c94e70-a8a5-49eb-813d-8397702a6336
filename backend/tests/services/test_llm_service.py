import pytest
import asyncio
from backend.src.llm.siliconflow import BaseLLMService, SiliconFlowService
from backend.src.services.llm_service import LLMServiceFactory


class TestLLMServiceFactory:

    tool_example = [{
                    'type': 'function',
                    'function': {
                        'name': 'get_weather',
                        'description': 'Get the current weather for a given city.',
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'city': {
                                    'type': 'string',
                                    'description': 'The name of the city to query weather for.',
                                },
                            },
                            'required': ['city'],
                        },
                    }
                },
                {
                    'type': 'function',
                    'function': {
                        'name': 'get_ticket_price',
                        'description': 'Get the train ticket price between two cities.',
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'starting_city': {
                                    'type': 'string',
                                    'description': 'Departure city name for the train.',
                                },
                                'destination_city': {
                                    'type': 'string',
                                    'description': 'Arrival city name for the train.',
                                },
                            },
                            'required': ['starting_city', 'destination_city'],
                        },
                    }
                }]

    def convert_function_json(self, original_func):
        """
        将原始函数JSON格式转换为新的格式

        参数:
        original_json: 原始JSON数据（列表格式）

        返回:
        转换后的JSON数据（列表格式）
        """
        converted_list = []

        for item in original_func:
            if item.get('type') == 'function' and 'function' in item:
                func_data = item['function']
                converted_func = {
                    "name": func_data.get('name', ''),
                    "description": func_data.get('description', ''),
                    "input_schema": func_data.get('parameters', {})
                }
                converted_list.append(converted_func)

        return converted_list

    def test_create_siliconflow_service(self):
        """测试使用默认配置创建SiliconFlow服务"""
        service = LLMServiceFactory.create_service()
        assert isinstance(service, SiliconFlowService)

    def test_service_call(self):
        service = LLMServiceFactory.create_service()
        call_resp = asyncio.run(service.call("你好"))
        print(call_resp)

    def test_service_call_with_context_default(self):
        service = LLMServiceFactory.create_service()
        call_with_contest_resp = asyncio.run(service.call_with_context(
            messages=[{"role": "user", "content": "查看今天杭州和上海的天气如何，并查询从杭州到上海的火车票价格"}],
            tools=self.tool_example
        ))
        print(call_with_contest_resp)

    def test_service_call_with_context(self):
        service = LLMServiceFactory.create_service(service_type="volcanoengine")
        call_with_contest_resp = asyncio.run(service.call_with_context(
            messages=[{"role": "system",
                       "content": "你是一名全能小助手，无所不能，可以执行各种函数功能，如加法计算、获取天气等。在需要时调用适当的函数来处理。对于回答不作任何解释"},
                      {"role": "user", "content": "2+3 等于多少"}],
            tools=[{
                "type": "function",
                "function": {
                    "name": "add_sum",
                    "description": "用于计算多个数累加求和的函数，比如从1累加到100",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "*args": {
                                "type": "array",
                                "items": {
                                    "type": "number"
                                },
                            },
                        },
                        "required": ["*args"],
                    },
                }
            }]
        ))
        print(call_with_contest_resp)

    def test_call_with_aliyuncs(self):
        service = LLMServiceFactory.create_service(service_type="aliyuncs")
        call_with_contest_resp = asyncio.run(service.call_with_context(
            messages=[{"role": "system",
                       "content": "你是一名全能小助手，无所不能，可以执行各种函数功能，如加法计算、获取天气等。在需要时调用适当的函数来处理。对于回答不作任何解释"},
                      {"role": "user", "content": "2+3 等于多少"}]
        ))
        print(call_with_contest_resp)

    def test_call_with_moonshot(self):
        service = LLMServiceFactory.create_service(service_type="moonshot")
        call_with_contest_resp = asyncio.run(service.call_with_context(
            messages=[{"role": "system",
                       "content": "你是一名全能小助手，无所不能，可以执行各种函数功能，如加法计算、获取天气等。在需要时调用适当的函数来处理。对于回答不作任何解释"},
                      {"role": "user", "content": "2+3 等于多少"}],
            tools=[{
                "type": "function",
                "function": {
                    "name": "add_sum",
                    "description": "用于计算多个数累加求和的函数，比如从1累加到100",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "*args": {
                                "type": "array",
                                "items": {
                                    "type": "number"
                                },
                            },
                        },
                        "required": ["*args"],
                    },
                }
            }]
        ))
        print(call_with_contest_resp)

    def test_call_with_fastrouter(self):
        service = LLMServiceFactory.create_service(service_type="fastrouter")
        call_with_contest_resp = asyncio.run(service.call_with_context(
            messages=[{"role": "user", "content": "查看今天杭州和上海的天气如何，并查询从杭州到上海的火车票价格"}],
            tools=self.convert_function_json(self.tool_example)
        ))
        print(call_with_contest_resp)
