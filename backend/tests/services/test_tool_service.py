from unittest import TestCase

from backend.src.schemas.session_data_schema import SessionData
from backend.src.services.tool_service import ToolService
from backend.src.session.session import Session
from backend.src.tools import BaseTool


class TestToolService(TestCase):
    def setUp(self):
        tool_prompts = [{'function': {
            'description': 'Use this tool to create a new file.The file and any parent directories will be created if not exist. NEVER use this tool to modify or overwrite existing files. Always first confirm that file_path does not exist before calling this tool.',
            'name': 'write_to_file', 'parameters': {
                'properties': {'file_content': {'description': 'The code content to write the file.', 'type': 'string'},
                               'file_path': {'description': 'The target file to create and write code to.',
                                             'type': 'string'}}, 'required': ['file_path', 'file_content'],
                'type': 'object'}}, 'type': 'function'},
            {'function': {'description': 'This is description', 'name': 'edit_file', 'parameters': {}},
             'type': 'function'},
            {'function': {'description': ' ', 'name': 'dummy_tool', 'parameters': {}}, 'type': 'function'}]

        self.session_data = SessionData(device_id="1", session_id="2", interaction_id="3")
        self.session = Session(self.session_data)
        self.service = ToolService(session=self.session)
        self.service.add_client_tool_by_prompt(tool_prompts)

    def test_return_tool_list_with_tool_name(self):
        tool_json = self.service.get_tools_prompt(["write_to_file"])
        tool_data = tool_json[0]
        print(f"\n {tool_data}")
        self.assertTrue(tool_data["type"] == "function")
        self.assertTrue(tool_data["function"]["name"] == "write_to_file")

    def test_return_all_tool(self):
        tool_json = self.service.get_tools_prompt()
        for tool_info in tool_json:
            tool = BaseTool(**tool_info["function"])
            print(tool)

    def test_execute_codebase_tool(self):
        import asyncio
        tool_result = asyncio.run(
            self.service.execute(
                tool_name="codebase",
                tool_params={
                    "query": "时间展示的ui",
                    "top_k": 10
                },
                timeout=5
            )
        )
        print(f"\n{tool_result=}")

    def test_execute_knowledge_tool(self):
        import asyncio
        tool_result = asyncio.run(
            self.service.execute(
                tool_name="search_harmony_knowledge",
                tool_params={
                    "query": "倒计时文本框的api"
                },
                timeout=5
            )
        )
        print(f"\n{tool_result=}")

    def test_mcp_client(self):
        mcp_config_ = {
            "mcpServers": {
                "calendar": {
                    "transport": "http",
                    "url": "https://calendar.example.com/mcp"
                },
                "hello_server": {
                    "transport": "stdio",
                    "command": "python ",
                    "args": ["./services/mcp_server/mcp_hello_server.py"]
                },

                "good_server": {
                    "transport": "stdio",
                    "command": "python ",
                    "args": ["./services/mcp_server/mcp_good_server.py"]
                }
            }
        }
        import asyncio
        asyncio.run(
            self.service.initialize_mcp_servers(mcp_config_)
        )
