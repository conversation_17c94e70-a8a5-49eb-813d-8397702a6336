<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitFun Composer Console</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        /* 自动滚动开关样式 */
        .auto-scroll-container {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
            margin-left: 8px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 20px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked+.slider {
            background-color: #4CAF50;
        }

        input:checked+.slider:before {
            transform: translateX(20px);
        }

        #chat-container {
            display: flex;
            flex-direction: column;
            height: 85vh;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        #output-area {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
            background-color: #fafafa;
            white-space: pre-wrap;
        }
         /* 新增导出按钮样式 */
        #export-button {
            padding: 0 20px;
            background-color: #673ab7;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
            margin-left: 10px;
        }
        #export-button:hover {
            background-color: #5e35b1;
        }

        /* 调整按钮容器布局 */
        #input-container {
            display: flex;
            gap: 4px; /* 缩小按钮间距 */
            padding: 12px;
            background-color: #eee;
            border-top: 1px solid #ddd;
            align-items: center;
        }
        #user-input {
            flex-grow: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
            font-size: 14px;
            min-width: 60%;
        }
        #send-button {
            padding: 0 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        #send-button:hover {
            background-color: #45a049;
        }
        #stop-button {
            padding: 0 20px;
            background-color: #ff9800;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        #stop-button:hover {
            background-color: #e68a00;
        }
        #stop-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        #terminate-button {
            padding: 0 20px;
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        #terminate-button:hover {
            background-color: #d32f2f;
        }
        #clear-button {
            padding: 0 20px;
            background-color: #9e9e9e;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        #clear-button:hover {
            background-color: #757575;
        }
        .message {
            margin-bottom: 10px;
            max-width: 90%;
            padding: 10px 15px;
            border-radius: 18px;
            line-height: 1.5;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease-out;
        }
        .user-message {
            background-color: #e3f2fd;
            color: #0d47a1;
            margin-left: auto;
            border-bottom-right-radius: 5px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .script-message {
            background-color: #f1f1f1;
            color: #333;
            margin-right: auto;
            border-bottom-left-radius: 5px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .system-message {
            background-color: #ffebee;
            color: #c62828;
            margin: 8px auto;
            border-radius: 5px;
            text-align: center;
            font-size: 0.9em;
            padding: 6px 10px;
        }
        .markdown-content {
            overflow-x: auto;
            line-height: 1.5;
        }
        .markdown-content p {
            margin: 0.3em 0;
        }
        .markdown-content pre {
            max-width: 100%;
            overflow-x: auto;
            padding: 0.8em;
            margin: 0.5em 0;
            background-color: #f8f8f8;
            border-radius: 5px;
        }
        .markdown-content code {
            font-family: 'Courier New', monospace;
            background-color: #f8f8f8;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .markdown-content ul,
        .markdown-content ol {
            margin: 0.3em 0;
            padding-left: 1.5em;
        }

        .markdown-content li {
            margin: 0.2em 0;
        }
        .markdown-content blockquote {
            margin: 0.5em 0;
            padding: 0.2em 1em;
            border-left: 3px solid #ddd;
        }
        .timestamp {
            font-size: 0.7em;
            color: #757575;
            margin-top: 3px;
            text-align: right;
        }
        /* 反馈按钮样式 */
        #feedback-container {
            display: none;
            margin: 15px auto;
            padding: 12px;
            background-color: #e8f5e9;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            text-align: center;
            animation: fadeIn 0.5s ease-in-out;
            border-left: 4px solid #4CAF50;
        }
        
        #feedback-container p {
            margin: 0 0 10px 0;
            font-weight: bold;
            color: #2e7d32;
        }
        
        #feedback-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .success-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 20px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .success-btn:hover {
            background-color: #45a049;
            transform: scale(1.05);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .fail-btn {
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 20px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .fail-btn:hover {
            background-color: #d32f2f;
            transform: scale(1.05);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }
        #output-area::-webkit-scrollbar {
            width: 6px;
        }
        #output-area::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        #output-area::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }
        #output-area::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <h1 style="color: #333; text-align: center;">BitFun Composer Console</h1>

    <div class="auto-scroll-container">
        <span>自动滚动到最新消息</span>
        <label class="switch">
            <input type="checkbox" id="auto-scroll-toggle" checked>
            <span class="slider"></span>
        </label>
    </div>

    <div id="chat-container">
        <div id="output-area"></div>
        <!-- 反馈容器 -->
        <div id="feedback-container">
            <p>请对此次修改进行反馈：</p>
            <div id="feedback-buttons">
                <button class="success-btn" onclick="sendFeedback(true)">修改成功</button>
                <button class="fail-btn" onclick="sendFeedback(false)">修改失败</button>
            </div>
        </div>
        <div id="input-container">
            <input type="text" id="user-input" placeholder="Type your command here..." autocomplete="off">
            <button id="send-button" onclick="sendMessage()">Send</button>
            <button id="stop-button" onclick="stopScript()">Stop</button>
            <button id="terminate-button" onclick="terminateScript()">Terminate</button>
            <button id="export-button" onclick="exportChat()">Export</button>
            <button id="clear-button" onclick="clearOutput()">Clear</button>
        </div>
    </div>

    <script>
        // 特殊标识符，用于触发反馈按钮
        const FEEDBACK_IDENTIFIER = "##COMPOSER##FEEDBACK_REQUIRED##";
        let feedbackContainer;
        
        // DOM加载完成后初始化元素引用
        document.addEventListener('DOMContentLoaded', function() {
            feedbackContainer = document.getElementById('feedback-container');
        });
        
        // 配置marked.js
        marked.setOptions({
            breaks: true,
            gfm: true,
            highlight: function(code, lang) {
                const language = hljs.getLanguage(lang) ? lang : 'plaintext';
                return hljs.highlight(code, { language }).value;
            }
        });

        // 自动滚动变量和开关
        let autoScrollEnabled = true;

        // 自动滚动到底部的函数
        function scrollToBottom() {
            if (autoScrollEnabled) {
                outputArea.scrollTop = outputArea.scrollHeight;
            }
        }

        const socket = io();
        const outputArea = document.getElementById('output-area');
        const userInput = document.getElementById('user-input');

        // 获取当前时间
        function getCurrentTime() {
            const now = new Date();
            return now.toLocaleTimeString();
        }

        // 创建消息元素
        function createMessageElement(content, type) {
            const messageDiv = document.createElement('div');
            const contentDiv = document.createElement('div');
            const timestampDiv = document.createElement('div');

            messageDiv.className = `message ${type}`;

            // 压缩多余空行
            const compactContent = cleanANSI(content.replace(/\n\s*\n/g, '\n'));

            contentDiv.className = 'markdown-content';
            contentDiv.innerHTML = marked.parse(compactContent);

            timestampDiv.className = 'timestamp';
            timestampDiv.textContent = getCurrentTime();

            messageDiv.appendChild(contentDiv);
            messageDiv.appendChild(timestampDiv);
            return messageDiv;
        }

        // 接收并显示输出
        socket.on('output', function(data) {
            // 只有当数据不包含FEEDBACK_IDENTIFIER时才显示
            if (!data.data.includes(FEEDBACK_IDENTIFIER)) {
                const messageType = 'script-message';
                const messageElement = createMessageElement(data.data, messageType);
                outputArea.appendChild(messageElement);
            }

            // 检查是否包含特殊标识符
            if (data.data.includes(FEEDBACK_IDENTIFIER)) {
                // 确保feedbackContainer已初始化
                if (!feedbackContainer) {
                    feedbackContainer = document.getElementById('feedback-container');
                }
                
                // 显示反馈按钮
                if (feedbackContainer) {
                    feedbackContainer.style.display = 'block';
                    // 自动滚动到反馈按钮
                    scrollToBottom();
                } else {
                    console.error('Feedback container not found');
                }
            }

            // 高亮代码块
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightElement(block);
            });

            // 自动滚动到最新消息
            scrollToBottom();
        });

        // 发送反馈
        function sendFeedback(isSuccess) {
            // 发送反馈到后端
            fetch('/send_feedback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    input: isSuccess ? "成功" : "失败"
                })
            })
            .then(response => response.json())
            .then(data => {
                // 显示反馈已发送的消息
                const feedbackMsg = createMessageElement(
                    isSuccess ? "✅ 已发送：修改成功" : "❌ 已发送：修改失败", 
                    'system-message'
                );
                outputArea.appendChild(feedbackMsg);
                
                // 隐藏反馈按钮
                feedbackContainer.style.display = 'none';
                
                // 滚动到最新消息
                scrollToBottom();
            })
            .catch(error => {
                const errorElement = createMessageElement(`Error: ${error.message}`, 'system-message');
                outputArea.appendChild(errorElement);
                scrollToBottom();
            });
        }

        // 发送消息
        function sendMessage() {
            const message = userInput.value.trim();
            if (message) {
                // 显示用户消息
                const userMessageElement = createMessageElement(message, 'user-message');
                outputArea.appendChild(userMessageElement);
                scrollToBottom();

                // 发送到服务器
                fetch('/send_input', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({input: message})
                }).catch(error => {
                    const errorElement = createMessageElement(`Error: ${error.message}`, 'system-message');
                    outputArea.appendChild(errorElement);
                    scrollToBottom();
                });

                // 隐藏反馈按钮
                feedbackContainer.style.display = 'none';
                userInput.value = '';
            }
        }

        // 导出聊天记录
        function exportChat() {
            // 获取所有消息内容
            const messages = Array.from(document.querySelectorAll('.message')).map(msg => {
                const type = msg.classList.contains('user-message') ? 'User' :
                            msg.classList.contains('system-message') ? 'System' : 'Script';
                const time = msg.querySelector('.timestamp').textContent;
                const content = msg.querySelector('.markdown-content').textContent;
                return `[${time}] ${type}: ${content}`;
            }).join('\n\n');

            // 创建Blob对象
            const blob = new Blob([messages], { type: 'text/plain;charset=utf-8' });

            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `chatlog_${new Date().toISOString().replace(/[:.]/g, '-')}.txt`;

            // 触发下载
            document.body.appendChild(a);
            a.click();

            // 清理
            setTimeout(() => {
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 100);

            // 显示导出确认消息
            const exportMsg = createMessageElement("Chat log exported successfully", 'system-message');
            outputArea.appendChild(exportMsg);
            scrollToBottom();
        }

        // 停止脚本（立即中止）
        function stopScript() {
            const stopMsg = createMessageElement("Sending immediate stop signal...", 'system-message');
            outputArea.appendChild(stopMsg);
            scrollToBottom();

            document.getElementById('stop-button').disabled = true;

            fetch('/stop_script', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                const responseMsg = createMessageElement(`Stop status: ${data.status}`, 'system-message');
                outputArea.appendChild(responseMsg);
                scrollToBottom();
            })
            .catch(error => {
                const errorElement = createMessageElement(`Error: ${error.message}`, 'system-message');
                outputArea.appendChild(errorElement);
                scrollToBottom();
            })
            .finally(() => {
                document.getElementById('stop-button').disabled = false;
            });
        }

        function cleanANSI(text) {
            return text.replace(/\x1B\[[0-?]*[ -/]*[@-~]/g, '');
        }

        // 终止脚本（优雅退出）
        function terminateScript() {
            const terminateMsg = createMessageElement("Sending graceful termination signal...", 'system-message');
            outputArea.appendChild(terminateMsg);
            scrollToBottom();

            fetch('/terminate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                const responseMsg = createMessageElement(`Termination status: ${data.status}`, 'system-message');
                outputArea.appendChild(responseMsg);
                scrollToBottom();
            })
            .catch(error => {
                const errorElement = createMessageElement(`Error: ${error.message}`, 'system-message');
                outputArea.appendChild(errorElement);
                scrollToBottom();
            });
        }

        // 清理输出区域
        function clearOutput() {
            outputArea.innerHTML = '';
            // 同时隐藏反馈按钮
            feedbackContainer.style.display = 'none';
        }

        // 允许按Enter键发送
        userInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // 自动聚焦输入框
        userInput.focus();

        // 自动滚动开关事件监听
        document.getElementById('auto-scroll-toggle').addEventListener('change', function (e) {
            autoScrollEnabled = e.target.checked;
            if (autoScrollEnabled) {
                scrollToBottom(); // 如果启用了自动滚动，立即滚动到底部
            }
        });

        // 监听滚动事件，当用户手动滚动到底部时，自动启用自动滚动
        outputArea.addEventListener('scroll', function () {
            // 检查是否滚动到底部（允许有5px的误差）
            const isAtBottom = outputArea.scrollHeight - outputArea.scrollTop - outputArea.clientHeight < 5;

            if (isAtBottom && !autoScrollEnabled) {
                autoScrollEnabled = true;
                document.getElementById('auto-scroll-toggle').checked = true;
            }
        });

    </script>
</body>
</html>