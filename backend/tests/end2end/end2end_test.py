import argparse
import asyncio
import hashlib
import json
import os
import pathlib
import sys
import threading
import time
import uuid

sys.path.append(str(pathlib.Path(__file__).parent.parent.parent.parent))
import websockets
from websockets import ConnectionClosedOK, ConnectionClosedError

from backend.src.core.config import settings
from backend.src.core.logging import get_module_logger, setup_logging

from backend.src.tools.builtin_tool_collection import BuiltInToolCollection
from backend.tests.end2end.tools import WriteFileTool, ListDirTool, ViewFileTool, GrepSearchTool, FindPathByPatternTool, \
    EditFileTool, RemoveFileTool, CheckCompilationTool, ManHoleTool, CodebaseTool, CodebaseInsightTool, call_codebase_refresh_index

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

def print_on_web(msg):
    msg = msg.replace('\n', '\\n')
    logger.info(f"[web_log]{msg}")

class DevEcoNextClientThread(threading.Thread):
    def __init__(self, message):
        super(DevEcoNextClientThread, self).__init__()
        self.message = message
        self.update_codebase_flag = False

    def run(self):
        msg_data = self.message.get("data", {})
        resp = msg_data.get("msg", {})
        msg_id = msg_data.get("msg_id")
        func_id = msg_data.get("msg_type", "")

        if func_id == "end":
            return

        if func_id == "tool_use":
            tool_name = resp.get("tool_name")
            params = resp.get("params")
            logger.info(f"端侧使用工具\n工具名称：{tool_name}\n工具参数：{params}")

            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                if tool_name == "search_codebase" and self.update_codebase_flag:
                    loop.run_until_complete(call_codebase_refresh_index())
                    self.update_codebase_flag = False
                # 运行异步任务
                tool_result = loop.run_until_complete(built_in_collection.execute(tool_name, **params))
                logger.info(f"{tool_result=}")
                if tool_name in ["edit_file", "write_to_file"] and tool_result.error is None:
                    self.update_codebase_flag = True
            except Exception as e:
                logger.error(f"Error when client execute tool: {e}")
                raise
            finally:
                # 关闭事件循环
                loop.close()

            ret_msg = {
                "session":
                    {
                        "device_id": device_id,
                        "session_id": session_id,
                        "interaction_id": interaction_id,
                    },
                "data": {
                    "msg_type": "tool_response",
                    "msg_id": msg_id,
                    "msg": tool_result.model_dump()
                }
            }
            # 返回工具执行结果
            with msg_list_lock:
                msg_list.append(ret_msg)

        # 打印执行步骤等信息
        elif func_id == "response":
            logger.info(f"Composer返回: {resp.get('msg')}")
            print_on_web(f"{resp.get('msg')}")


async def receive_msg(websocket):
    try:
        while True:
            message_str = await websocket.recv()
            message = json.loads(message_str)
            # logger.info(f"收到消息: {message}")

            if message.get("data", {}).get("msg_type") == "end":
                global end_sign
                end_sign = True
                websocket.close()
            try:
                t = DevEcoNextClientThread(message)
                t.start()
            except Exception as e:
                logger.error(f"端侧执行报错：{e}", exc_info=True)
    except ConnectionClosedOK:
        # 连接正常关闭，让主循环处理
        pass
    except ConnectionClosedError as e:
        logger.error(f"接收消息时连接关闭: {e}")
        # 抛给主循环处理重试
        raise
    except Exception as e:
        logger.error(f"Connect Error: {str(e)}")


async def send_msg(websocket):
    try:
        while not end_sign:
            if len(msg_list) > 0:
                msg = msg_list.pop(0)
                # logger.info(f"发送消息: {msg}")
                await websocket.send(json.dumps(msg, ensure_ascii=False))
            await asyncio.sleep(1)
    except ConnectionClosedOK:
        # 连接正常关闭，让主循环处理
        pass
    except ConnectionClosedError as e:
        logger.error(f"接收消息时连接关闭: {e}")
        # 抛给主循环处理重试
        raise
    except Exception as e:
        logger.error(f"Connect Error: {str(e)}")


async def receive_msg_from_web():
    while True:
        msg = await asyncio.get_event_loop().run_in_executor(None, input, "")
        if "[msg]" in msg:
            with msg_list_lock:
                msg_list.append({
                "session":
                    {
                        "device_id": device_id,
                        "session_id": session_id,
                        "interaction_id": interaction_id,
                    },
                "data": {
                    "msg_type": "supplemented_info",
                    "msg_id": "111",
                    "msg":{
                        "query": msg.removeprefix("[msg]")
                    },
                }
            })
        elif "[end]" in msg:
            with msg_list_lock:
                msg_list.append({
                "session":
                    {
                        "device_id": device_id,
                        "session_id": session_id,
                        "interaction_id": interaction_id,
                    },
                "data": {
                    "msg_type": "end",
                    "msg_id": "111",
                    "msg": ""
                }
            })
        elif "[feedback]" in msg:
            with msg_list_lock:
                msg_list.append({
                "session":
                    {
                        "device_id": device_id,
                        "session_id": session_id,
                        "interaction_id": interaction_id,
                    },
                "data": {
                    "msg_type": "feedback",
                    "msg_id": "111",
                    "msg":{
                        "query": msg.removeprefix("[feedback]")
                    },
                }
            })
        elif "[stop]" in msg:
            with msg_list_lock:
                msg_list.append({
                "session":
                    {
                        "device_id": device_id,
                        "session_id": session_id,
                        "interaction_id": interaction_id,
                    },
                "data": {
                    "msg_type": "stop",
                    "msg_id": "111",
                    "msg": ""
                }
            })
        elif "[answer]" in msg:
            man_hole_tool.input_answer(msg.removeprefix("[answer]"))


async def init_websocket_client(url):
    try:
        async with websockets.connect(
                url,
                ping_interval=10,  # 发送心跳间隔（秒）
                ping_timeout=None,  # 心跳超时时间（秒）
        ) as websocket:
            logger.info(f"已连接到 {url}")
            await asyncio.sleep(0.1)
            msg = ask()
            msg_list.append(msg)
            await call_codebase_refresh_index()

            send_task = asyncio.create_task(send_msg(websocket))
            receive_task = asyncio.create_task(receive_msg(websocket))
            receive_msg_task = asyncio.create_task(receive_msg_from_web())
            await send_task
            await receive_task
            await receive_msg_task

    except websockets.exceptions.ConnectionClosedOK:
        logger.info("连接已正常关闭")
    except websockets.exceptions.ConnectionClosedError as e:
        logger.error(f"连接错误: {e}")
    except Exception as e:
        logger.error(f"发生错误: {e}")


def get_short_device_id(length=8):
    mac = uuid.getnode()
    mac_str = str(mac).encode('utf-8')

    # 使用 MD5 哈希（16 字节 = 32 字符）
    hash_object = hashlib.md5(mac_str)
    hash_hex = hash_object.hexdigest()

    # 截取前 N 个字符作为短 ID
    return hash_hex[:length]


def ask():
    global interaction_id
    interaction_id = str(int(time.time() * 1000))
    project_path = input("请输入工程目录: ").removeprefix("[proj]")
    if not project_path:
        project_path = default_project_path
    query = input("请输入任务：").removeprefix("[query]")

    os.environ["PROJECT_PATH"] = project_path

    print_on_web(f"{query}。工程目录在 {project_path}")

    msg = {
        "session":
            {
                "device_id": device_id,
                "session_id": session_id,
                "interaction_id": interaction_id,
            },
        "data": {
            "msg_type": "query_agent",
            "msg_id": "",
            "msg": {
                "query": f"{query}。工程目录在 {project_path}"
            },
        },
        "tools": built_in_collection.build_tool_prompt()
    }
    return msg


man_hole_tool = ManHoleTool()

if __name__ == '__main__':
    setup_logging()

    parser = argparse.ArgumentParser(description="DevEcoNextClient websocket test")
    parser.add_argument('--composer_ip', type=str, default=settings.COMPOSER_IP, help="Composer server IP address")
    parser.add_argument('--composer_port', type=str, default=settings.COMPOSER_PORT, help="Composer server port")
    args = parser.parse_args()

    composer_url = f"ws://{args.composer_ip}:{args.composer_port}/query_agent"
    default_project_path = ""
    end_sign = False
    msg_list = []
    msg_list_lock = threading.Lock()
    interaction_id = ""
    device_id = get_short_device_id(6)
    session_id = uuid.uuid4().hex[:4]
    # 配置编译需要的环境变量
    os.environ["NODE_PATH"] = "/Applications/DevEco-Studio.app/Contents/tools/node/bin/node"
    os.environ["HVIGORW_PATH"] = "/Applications/DevEco-Studio.app/Contents/tools/hvigor/bin/hvigorw.js"
    os.environ["DEVECO_SDK_HOME"] = "/Applications/DevEco-Studio.app/Contents/sdk"

    # 添加端侧的工具
    built_in_collection = BuiltInToolCollection(
        [WriteFileTool(),
         ListDirTool(), GrepSearchTool(), FindPathByPatternTool(),
         # RemoveFileTool(),
         man_hole_tool,
         ViewFileTool(), EditFileTool(),
         CodebaseTool(),
         CodebaseInsightTool(),
         CheckCompilationTool()]
    )

    asyncio.run(init_websocket_client(composer_url))
