import os
import re
from pathlib import Path

from backend.src.core.logging import get_module_logger
from backend.src.tools import BaseExecTool, ToolResult
from backend.tests.end2end.tools.file_tool import convert_to_end_abs_path

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)


class GrepSearchTool(BaseExecTool):
    name: str = "grep_search"
    description: str = """使用本方法搜索文件内容关键字，不能搜索文件名或目录名。
待搜索关键字使用ripgrep命令"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "search_directory": {
                "type": "string",
                "description": "待搜索的根目录.",
            },
            "match_pattern": {
                "type": "string",
                "description": "待查询关键字，使用正则表达式来表示.",
            },
            "case_sensitive": {
                "type": "boolean",
                "description": "是否大小写敏感，默认False.",
            }
        },
        "required": ["search_directory", "match_pattern"],
    }

    @staticmethod
    def search_files_for_pattern(
            root_dir: str,
            pattern: str,
            case_sensitive: bool = False,
            max_depth: int = None,
            file_pattern: str = r".*",  # 默认搜索所有文件
            encoding: str = "utf-8"
    ) -> str:
        """
        在目录中搜索包含特定正则表达式pattern的文件

        参数:
            root_dir: 要搜索的根目录
            pattern: 要搜索的正则表达式模式
            file_pattern: 要搜索的文件名模式(正则表达式)
            max_depth: 最大搜索深度(None表示无限制)
            case_sensitive: 是否区分大小写
            encoding: 文件编码格式

        返回:
            匹配结果列表，每个元素是元组(文件路径, 行号, 匹配行内容)
        """
        root_dir = convert_to_end_abs_path(root_dir)
        root_path = Path(root_dir).absolute()
        if not root_path.exists():
            raise FileNotFoundError(f"目录不存在: {root_dir}")

        # 编译搜索pattern
        content_regex = re.compile(pattern, 0 if case_sensitive else re.IGNORECASE)
        file_regex = re.compile(file_pattern, 0 if case_sensitive else re.IGNORECASE)

        matches = []

        # 需要排除的目录名集合
        EXCLUDED_DIRS = {"build", "oh_modules"}
        def _search(current_path: Path, current_depth: int):
            if max_depth is not None and current_depth > max_depth:
                return

            for item in current_path.iterdir():
                if item.is_file() and file_regex.search(item.name):
                    try:
                        with open(item, "r", encoding=encoding) as f:
                            for line_num, line in enumerate(f, 1):
                                if content_regex.search(line):
                                    matches.append(
                                        f"路径：{str(item.absolute())}\n第{line_num}行\n行内容为：{line.strip()}\n-------"
                                    )
                    except (UnicodeDecodeError, PermissionError) as e:
                        print(f"无法读取文件 {item}: {e}")
                        continue
                elif item.is_dir():
                    if item.name in EXCLUDED_DIRS or item.name.startswith("."):
                        return
                    _search(item, current_depth + 1)

        _search(root_path, 0)
        return "\n".join(matches)

    async def execute(self, **kwargs) -> ToolResult:
        search_directory = kwargs.get("search_directory", "")
        match_pattern = kwargs.get("match_pattern", "")
        case_sensitive = kwargs.get("case_sensitive", False)

        if not os.path.exists(search_directory):
            return ToolResult(error=f"Root path {search_directory} does not exist")

        return ToolResult(content=self.search_files_for_pattern(search_directory, match_pattern, case_sensitive))


if __name__ == "__main__":
    logger.info(GrepSearchTool.search_files_for_pattern("C:/Users/<USER>/Desktop/projects/test", "1"))
