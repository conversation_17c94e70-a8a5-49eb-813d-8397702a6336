import threading

from inputimeout import inputimeout, TimeoutOccurred

from backend.src.core.logging import get_module_logger
from backend.src.tools import BaseExecTool, ToolResult

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)


class ManHoleTool(BaseExecTool):
    name: str = "show_msg_to_user_and_wait_for_response"
    description: str = """对用户展示信息，并等待回复。"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "对用户展示的信息",
            }
        },
        "required": ["query"]
    }
    cond: threading.Condition = threading.Condition()
    answer: str = "用户无响应"

    async def execute(self, **kwargs) -> ToolResult:
        with self.cond:
            self.cond.wait(timeout=300)
        return ToolResult(content=f"用户回答：{self.answer}")

    def input_answer(self, answer: str):
        self.answer = answer
        with self.cond:
            self.cond.notify()

if __name__ == "__main__":
    import asyncio
    result = asyncio.run(ManHoleTool().execute(**{
        "query": "你开心不"
    }))
    logger.info(result)
