import fnmatch
import json
import os
import re
from pathlib import Path

from backend.src.core.logging import get_module_logger
from backend.src.tools import BaseExecTool, ToolResult
from backend.tests.end2end.tools.file_tool import convert_to_end_abs_path

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)


class FindPathByPatternTool(BaseExecTool):
    name: str = "find_path_by_name_pattern"
    description: str = """使用本方法查询符合特定pattern的文件名或目录名，类似Linux的'find'命令。返回匹配文件相对于root_directory的相对路径。

"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "root_directory": {
                "type": "string",
                "description": "待查询的根目录，应是绝对路径.",
            },
            "pattern": {
                "type": "string",
                "description": "查询的匹配范式。 Glob 模式，通配符匹配",
            },
            "max_depth": {
                "type": "number",
                "description": "查询的最大深度，默认为3.",
            }
        },
        "required": ["root_directory", "pattern"],
    }

    @staticmethod
    def find_files(
            root_dir: str,
            pattern: str,
            max_depth: int = -1,
            case_sensitive: bool = True
    ):
        """
        模拟Linux find命令的通配符匹配功能

        :param root_dir: 搜索的根目录路径
        :param pattern: 通配符模式 (如 *.txt)
        :param max_depth: 最大搜索深度 (-1表示无限制)
        :param case_sensitive: 是否大小写敏感
        :return: 匹配文件的相对路径集合
        """
        matched_files = set()
        root_dir = convert_to_end_abs_path(root_dir)

        # 根据大小写敏感设置选择匹配函数
        match_fn = fnmatch.fnmatch if case_sensitive else fnmatch.fnmatchcase

        for root, dirs, files in os.walk(root_dir):
            # 计算当前深度
            current_depth = root[len(root_dir):].count(os.sep)
            if max_depth != -1 and current_depth > max_depth:
                del dirs[:]  # 不再遍历子目录
                continue

            for filename in files:
                if match_fn(filename, pattern):
                    # 获取相对路径
                    full_path = os.path.join(root, filename)
                    rel_path = os.path.relpath(full_path, start=root_dir)
                    matched_files.add(rel_path)

        return json.dumps(list(matched_files), ensure_ascii=False)

    async def execute(self, **kwargs) -> ToolResult:
        root_directory = kwargs.get("root_directory", "")
        pattern = kwargs.get("pattern", "")
        max_depth = kwargs.get("max_depth", 3)

        if not os.path.exists(root_directory):
            return ToolResult(error=f"Root path {root_directory} does not exist")

        return ToolResult(content=self.find_files(root_directory, pattern, max_depth))


if __name__ == "__main__":
    logger.info(FindPathByPatternTool.find_files("C:/Users/<USER>/Desktop/projects", "test", 10))
    logger.info(FindPathByPatternTool.find_files("C:/Users/<USER>/Desktop/projects", "*.ets", 10))
