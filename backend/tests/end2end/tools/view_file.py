import os

from backend.src.core.logging import get_module_logger
from backend.src.tools import BaseExecTool, ToolResult
from backend.tests.end2end.tools.file_tool import convert_to_end_abs_path

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)


class ViewFileTool(BaseExecTool):
    name: str = "view_file"
    description: str = """使用本方法读取文件的指定行范围的信息。
你需要指定读取的行范围，行号从0开始标识，-1代表最尾行。
注意：本方法读取的信息仅是片段，如果遇到缺失信息，需要读取更大范围或使用其他搜索方式定位。"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "absolute_path": {
                "type": "string",
                "description": "待读取文件的绝对路径.",
            },
            "start_line": {
                "type": "number",
                "description": "文件读取范围的起始位置，默认为0.",
            },
            "end_line": {
                "type": "number",
                "description": "文件读取范围的终止位置，默认为-1.",
            }
        },
        "required": ["absolute_path"]
    }

    @staticmethod
    def read_lines_range(file_path, start_line, end_line):
        """
        读取文件的特定行范围（包括起始行和终止行）

        参数:
            file_path (str): 文件路径
            start_line (int): 起始行号（从0开始）
            end_line (int): 终止行号（包含在内）

        返回:
            str: 文件行范围内容

        异常:
            ValueError: 如果行号范围无效
            FileNotFoundError: 如果文件不存在
        """
        # 验证行号范围
        if start_line < 0:
            return ToolResult(error="起始行号不能小于0")
        if end_line < start_line and end_line != -1:
            return ToolResult(error="终止行号不能小于起始行号")

        if not os.path.exists(file_path):
            return ToolResult(error=f"File path {file_path} does not exist")
        if not os.path.isfile(file_path):
            return ToolResult(error=f"路径{file_path}指向的的不是个文件。")


        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
            line_cnt = len(lines)
            if start_line >= line_cnt:
                return ToolResult(error=f"起始行号{start_line}超出文件行数{line_cnt}")
            end_line = min(line_cnt - 1, end_line)
            return ToolResult(content="".join(lines[start_line:end_line]))

    async def execute(self, **kwargs) -> ToolResult:
        absolute_path = kwargs.get("absolute_path", "")
        absolute_path = convert_to_end_abs_path(absolute_path)
        start_line = kwargs.get("start_line", 0)
        end_line = kwargs.get("end_line", -1)

        if not os.path.exists(absolute_path):
            return ToolResult(error=f"Root path {absolute_path} does not exist")

        return self.read_lines_range(absolute_path, start_line, end_line)


if __name__ == "__main__":
    logger.info(ViewFileTool.read_lines_range("C:/Users/<USER>/Desktop/projects/test/test.json", 1, 10))
