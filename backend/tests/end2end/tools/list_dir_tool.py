import os
from pathlib import Path

from backend.src.core.logging import get_module_logger
from backend.src.tools import BaseExecTool, ToolResult
from backend.tests.end2end.tools.file_tool import convert_to_end_abs_path

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)


class ListDirTool(BaseExecTool):
    name: str = "list_dir"
    description: str = """使用本方法查询目录结构。查询返回结果包含目录名、文件名，以及他们的层级关系。"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "root_directory": {
                "type": "string",
                "description": "待查询的根目录.",
            },
            "max_depth": {
                "type": "number",
                "description": "最大查询深度，默认为3。",
            }
        },
        "required": ["root_directory"]
    }

    @staticmethod
    def get_directory_structure(root_dir, max_depth=None):
        """
        生成目录结构的字符串表示，过滤隐藏目录和特定目录

        参数:
            root_dir (str): 要遍历的根目录
            max_depth (int, optional): 最大遍历深度。None表示无限制

        返回:
            str: 字符串形式的目录结构
        """
        root_dir = convert_to_end_abs_path(root_dir)
        root_dir = Path(root_dir).absolute()
        structure = []

        # 需要排除的目录名集合
        EXCLUDED_DIRS = {"build", "oh_modules"}

        def _build_structure(current_dir, current_depth, prefix=""):
            if max_depth is not None and current_depth > max_depth:
                return

            # 添加当前目录（如果是根目录则用绝对路径）
            if current_depth == 0:
                structure.append(f"{prefix}{str(current_dir)}")
            else:
                structure.append(f"{prefix}{current_dir.name}")

            # 准备下一级的prefix
            next_prefix = prefix + "    "

            # 获取并过滤目录
            dirs = sorted([
                d for d in current_dir.iterdir()
                if d.is_dir()
                   and not d.name.startswith(".")  # 排除隐藏目录
                   and d.name not in EXCLUDED_DIRS  # 排除特定目录
            ])

            # 处理目录
            for i, d in enumerate(dirs):
                is_last_dir = (i == len(dirs) - 1)
                connector = "└── " if is_last_dir else "├── "
                _build_structure(d, current_depth + 1, next_prefix + connector)

            # 获取并过滤文件（不显示隐藏文件）
            files = sorted([
                f for f in current_dir.iterdir()
                if f.is_file()
                   and not f.name.startswith(".")
            ])

            # 处理文件
            for i, f in enumerate(files):
                is_last = (i == len(files) - 1) and (len(dirs) == 0)
                connector = "└── " if is_last else "├── "
                structure.append(f"{next_prefix}{connector}{f.name}")

        _build_structure(root_dir, 0)
        return "\n".join(structure)

    async def execute(self, **kwargs) -> ToolResult:
        root_directory = kwargs.get("root_directory", "")
        max_depth = kwargs.get("max_depth", 3)

        if not os.path.exists(root_directory):
            return ToolResult(error=f"Root path {root_directory} does not exist")

        return ToolResult(content=self.get_directory_structure(root_directory, max_depth))


if __name__ == "__main__":
    logger.info(ListDirTool.get_directory_structure("C:/Users/<USER>/Desktop/projects/test"))
