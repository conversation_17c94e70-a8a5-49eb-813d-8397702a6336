import os
import re
import subprocess
import sys
import pathlib
root_dir = str(pathlib.Path(__file__).parent.parent.parent)
sys.path.append(root_dir)
from backend.src.tools import ToolResult, BaseExecTool
from backend.src.core.logging import get_module_logger

logger = get_module_logger(__name__)


class CheckCompilationTool(BaseExecTool):
    name: str = "check_compilation"
    description: str = """如果认为项目代码已经修改完成，请使用这个工具用作编译验证，来验证代码的正确性；
    如果编译验证失败，请重新检查所做的更改以及必要时回退代码"""
    parameters: dict = {
        "type": "object",
        "properties": {},
        "required": []
    }

    async def execute(self, **kwargs) -> ToolResult:
        # 通过系统环境变量获取 node_path 和 hvigorw_path
        node_path = os.environ.get("NODE_PATH")
        hvigorw_path = os.environ.get("HVIGORW_PATH")
        project_path = os.environ.get("PROJECT_PATH")

        if not node_path:
            return ToolResult(error="未设置 NODE_PATH 环境变量")
        if not hvigorw_path:
            return ToolResult(error="未设置 HVIGORW_PATH 环境变量")
        if not project_path:
            return ToolResult(error="未设置 PROJECT_PATH 环境变量")

        cmd = f"\"{node_path}\" \"{hvigorw_path}\" assembleHap --mode module -p product=default -p buildMode=debug --error --parallel --incremental --no-daemon"
        logger.info(f"执行命令: {cmd}")
        result = subprocess.run(cmd, cwd=project_path, stdin=subprocess.DEVNULL, stderr=subprocess.PIPE, stdout=subprocess.PIPE, shell=True)

        try:
            output = result.stdout.decode("utf-8")
        except UnicodeDecodeError:
            output = result.stdout.decode("gbk", errors="replace")
        try:
            error = result.stderr.decode("utf-8")
        except UnicodeDecodeError:
            error = result.stderr.decode("gbk", errors="replace")
        logger.info(f"执行命令结果: {output}")

        if "ERROR" not in error:
            logger.info("编译成功")
            return ToolResult(content="编译成功")
        else:
            logger.info(f"执行命令错误: {error}")
            # 过滤掉warning，减少冗余信息干扰
            error = re.sub(r'1 WARN:.*?((?=1 ERROR:))', '', error, flags=re.DOTALL)
            return ToolResult(content=f"编译失败: {error}")


if __name__ == '__main__':
    import asyncio
    os.environ["DEVECO_SDK_HOME"] = "/Applications/DevEco-Studio.app/Contents/sdk"
    os.environ["NODE_PATH"] = "/Applications/DevEco-Studio.app/Contents/tools/node/bin/node"
    os.environ["HVIGORW_PATH"] = "/Applications/DevEco-Studio.app/Contents/tools/hvigor/bin/hvigorw.js"
    os.environ["PROJECT_PATH"] = "/Users/<USER>/code/composer-evaluation/oh-example-composer/music-gathering"
    import asyncio

    result = asyncio.run(CheckCompilationTool().execute())
    print(result)
