import json
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional
from backend.src.tools import BaseExecTool, ToolResult
from backend.tests.end2end.config import e2e_settings
from backend.src.core.logging import get_module_logger

logger = get_module_logger(__name__)


class CodebaseTool(BaseExecTool):
    name: str = "search_codebase"
    description: str = """工具名称：
代码库检索工具`search_codebase`

工具用途：
将用户的自然语言问题，转换为一个高效、精准的代码库检索引擎查询。

输入参数：
query: 字符串，需要在Codebase服务中检索的内容。

query参数规范：
优先保留实体： 用户的查询中的具体实体是最高优先级的，必须原样保留。
实体包括： 文件名 (utils.js), 函数/类名 (PaymentService), 关键字符串字面量 ("支付成功")。
处理中文业务词： 如果查询中的中文词是业务概念或UI文本（如“优惠券”、“购物车”），最终查询必须同时包含中文原词及其可能的英文翻译/技术词汇。
泛化概念扩展： 对“逻辑”、“配置”、“处理”这类通用概念，进行同义词扩展。
遵守负向约束： 严格排除用户提到的“不要”、“排除”等内容。

调用示例：
示例 1: (处理中文业务词)
用户原始问题: "app里的优惠券功能在哪实现的？"
智能体思考过程:
中文业务词: 优惠券 -> 优惠券, coupon, voucher, discount
通用概念: "实现" -> logic, service, component, api, implementation
优化后的工具调用: search_codebase(query="优惠券 coupon voucher discount logic service component api implementation")

示例 2: (保留实体 - 文件名和函数名)
用户原始问题: "在 checkout.py 文件里 process_payment 函数的逻辑是怎样的？"
智能体思考过程:
实体: checkout.py, process_payment
通用概念: "逻辑" -> logic, implementation, definition
优化后的工具调用: search_codebase(query="checkout.py process_payment logic implementation definition")

示例 3: (保留实体 - 关键字符串)
用户原始问题: "处理‘支付超时’这个错误的逻辑在哪？"
智能体思考过程:
实体: '支付超时'
通用概念: "错误处理" -> error, exception, handle, catch, logic, timeout
优化后的工具调用: search_codebase(query="支付超时 payment timeout error exception handle catch logic")

示例 4: (通用查询 + 负向约束)
用户原始问题: "登录咋做的？别给我看测试文件。"
智能体思考过程:
通用概念: "登录" -> login, auth, session, token; "咋做的" -> implementation, logic, handler
负向约束: 排除 test
优化后的工具调用: search_codebase(query="login auth session token implementation logic handler")

输出格式：
Markdown，返回相关的代码片段，需包括文件路径、相关度评分和代码内容。
"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": """一个保留了关键实体名词（如文件路径、函数名、符号名、具体代码）、并对中英文业务词和通用概念进行同义词扩展的关键词组合查询请求。"""
            },
        },
        "required": ["query"]
    }

    async def execute(self, **kwargs) -> ToolResult:
        """
        执行代码库查询
        
        Args:
            query: 查询字符串
            
        Returns:
            ToolResult: 包含查询结果的工具执行结果
        """
        query = kwargs.get("query", "").strip()

        top_k = kwargs.get("top_k", 10)
        base_url = kwargs.get("base_url", f"http://{e2e_settings.CODEBASE_IP}:{e2e_settings.CODEBASE_PORT}")
        
        if not query or len(query) < 3:
            return ToolResult(
                error="查询内容太少，请至少输入3个字符"
            )
        
        # 去掉可能的@codebase前缀
        if query.startswith("@codebase"):
            query = query.replace("@codebase", "").strip()
        
        try:
            # 构造请求
            request_data = {
                "query": query,
                "top_k": top_k,
            }
            
            # 发送HTTP请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{base_url}/query",
                    json=request_data,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        return ToolResult(
                            error=f"CodebaseQA服务请求失败 (状态码: {response.status}): {error_text}"
                        )
                    
                    result_data = await response.json()
                    
            # 处理和格式化结果
            formatted_result = self._format_codebase_results(result_data)
            
            return ToolResult(
                content=formatted_result
            )
            
        except aiohttp.ClientError as e:
            return ToolResult(
                error=f"网络请求失败: {str(e)}"
            )
        except asyncio.TimeoutError:
            return ToolResult(
                error="请求超时，请检查CodebaseQA服务是否正常运行"
            )
        except json.JSONDecodeError as e:
            return ToolResult(
                error=f"响应格式解析失败: {str(e)}"
            )
        except Exception as e:
            return ToolResult(
                error=f"执行查询时发生错误: {str(e)}"
            )
    
    def _format_codebase_results(self, result_data: Dict[str, Any]) -> str:
        """
        格式化代码库查询结果
        
        Args:
            result_data: 从CodebaseQA服务返回的原始数据
            
        Returns:
            str: 格式化后的结果字符串
        """
        query = result_data.get("query", "")
        results = result_data.get("results", [])
        
        if not results:
            return f"🔍 查询: {query}\n\n❌ 未找到相关的代码片段"
        
        formatted_output = [f"🔍 查询: {query}"]
        formatted_output.append(f"📊 找到 {len(results)} 个相关结果\n")
        
        for i, result in enumerate(results, 1):
            # 提取结果信息
            file_path = result.get("file_path", "未知文件")
            text = result.get("text", "无内容")
            score = result.get("score", 0)
            metadata = result.get("metadata", {})
            
            # 提取元数据
            start_line = metadata.get("start_line", 0)
            end_line = metadata.get("end_line", start_line)
            category = metadata.get("category", "")
            file_type = ("."+file_path).split(".")[-1]
            
            # 格式化单个结果
            result_section = [
                f"结果 {i}:",
                f"文件: {file_path}",
                f"位置: 第{start_line}-{end_line}行" if start_line else "",
                f"索引类型: {category}" if category else "",
                f"文件类型: {file_type}" if file_type else "",
                f"相关度: {score:.3f}",
                f"代码内容:",
                "```"
            ]
            result_section = [item for item in result_section if item]
            
            text = text.replace("\r", "")
            code_lines = text.split('\n')
            for line in code_lines:
                result_section.append(f"{line}")
            
            result_section.append("```")
            result_section.append("")  # 空行分隔
            
            formatted_output.extend(result_section)
        
        return "\n".join(formatted_output)
    
    async def health_check(self, base_url: str = f"http://{e2e_settings.CODEBASE_IP}:5001") -> bool:
        """
        检查CodebaseQA服务健康状态
        
        Args:
            base_url: 服务基础URL
            
        Returns:
            bool: 服务是否健康
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{base_url}/health",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
        except:
            return False


async def call_codebase_refresh_index():
    codebase_update_url = f"http://{e2e_settings.CODEBASE_IP}:{e2e_settings.CODEBASE_PORT}/indexes/update"
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                    f"{codebase_update_url}",
                    timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status != 200:
                    logger.error(
                        f"调用codebase刷新索引失败：status_code={response.status}, reason={response.reason}")
                else:
                    logger.info(f"调用codebase刷新索引成功")
    except Exception as e:
        logger.error(f"调用codebase刷新索引失败: {str(e)}")


# 工具使用示例
async def example_usage():
    """使用示例"""
    tool = CodebaseTool()
    # 示例1: 健康检查
    tool_instance = CodebaseTool()
    health = await tool_instance.health_check()
    print(f"示例1 - 健康检查: {'✅ 服务正常' if health else '❌ 服务异常'}")
    print("\n" + "="*80 + "\n")

    # 示例2: 基本查询
    result1 = await tool.execute(query="INTER_PORT")
    print("示例2 - 基本查询:")
    print(result1.content if result1.error is None else f"错误: {result1.error}")
    print("\n" + "="*80 + "\n")
    
    # 示例3: 带@codebase前缀的查询
    result2 = await tool.execute(query="启动服务", top_k=3)
    print("示例3 - 前缀查询:")
    print(result2.content if result2.error is None else f"错误: {result2.error}")
    print("\n" + "="*80 + "\n")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(example_usage())
