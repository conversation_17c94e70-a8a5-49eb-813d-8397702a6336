import asyncio
import base64
import os

from backend.src.core.logging import get_module_logger
from backend.src.tools import BaseExecTool, ToolResult

# 获取模块专用的日志记录器
logger = get_module_logger(__name__)

binary_file_extensions = [".exe", ".dll", ".png", ".jpg", ".pdf", ".zip"]


def is_binary_content(content: str) -> bool:
    # 检查是否包含不可打印字符或非 ASCII 字符
    return any(ord(char) < 32 and char not in "\t\n\r" for char in content)


def is_binary_file(file_path: str) -> bool:
    return any(file_path.lower().endswith(ext) for ext in binary_file_extensions)


def should_write_as_binary(file_path: str, content: str) -> bool:
    return is_binary_file(file_path) or is_binary_content(content)


def read_file(file_path):
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
            return content
    except Exception as e:
        raise Exception(e)


def convert_to_end_abs_path(file_path):
    project_path = os.path.normpath(os.environ.get("PROJECT_PATH", ""))
    file_path = os.path.normpath(file_path)
    file_abs_path = ""
    if not os.path.isabs(file_path):
        # 相对路径直接拼接到本地项目路径
        file_abs_path = os.path.join(project_path, file_path)
    else:
        file_abs_path = file_path
    # 统一路径分隔符
    return os.path.normpath(file_abs_path)


class WriteFileTool(BaseExecTool):
    name: str = "write_to_file"
    description: str = """使用这个工具可以创建一个新的文件，并且写入内容。
        文件和它的目录如果不存在的话都将被创建。
        这个工具只能创建新的文件，不能修改和删除已有的文件，所以在使用这个工具只要确保文件目录file_path不存在
        """
    parameters: dict = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "The target file to create and write code to.",
            },
            "file_content": {
                "type": "string",
                "description": "The code content to write the file.",
            }
        },
        "required": ["file_path", "file_content"]
    }

    async def execute(self, **kwargs) -> ToolResult:
        file_path = kwargs.get("file_path", "")
        file_content = kwargs.get("file_content", "")
        if file_path == "":
            return ToolResult(error="file_path cannot be empty")
        if os.path.exists(file_path):
            return ToolResult(error=f"File {file_path} already exists")
        file_path = convert_to_end_abs_path(file_path)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        if should_write_as_binary(file_path, file_content):
            try:
                with open(file_path, "wb") as f:
                    f.write(base64.b64decode(file_content.encode("utf-8")))
            except Exception as e:
                return ToolResult(error=f"write binary file fail: {str(e)}")
        else:
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(file_content)
            except Exception as e:
                return ToolResult(error=f"write file fail: {str(e)}")
        return ToolResult(content="write file succeed")


class EditFileTool(BaseExecTool):
    name: str = "edit_file"
    description: str = """使用这个工具可以编辑某个文件的内容。请注意：
    0. 在编辑前请确保已经读取到文件的最新内容
    1. original_code_snippet需要与目标文件中的一行或者多行连续完全匹配，注意空格数量和回车数量需**完全一致**
    2. 如果original_code_snippet在目标文件中不唯一，则将不会进行替换，请保证它具有适量的上下文
    3. 如果要编辑不连续的代码，请多次调用这个工具 
    """
    parameters: dict = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "需要编辑的目标文件路径",
            },
            "original_code_snippet": {
                "type": "string",
                "description": "目标文件中的需要被替换修改的原始代码块，注意空格和回车。",
            },
            "replaced_code_snippet": {
                "type": "string",
                "description": "将要替换原始代码块的代码，注意空格和回车",
            }
        },
        "required": ["file_path", "original_code_snippet", "replaced_code_snippet"]
    }

    def replace_code_block(self, original_codes, to_replace_codes, replaced_codes):
        print(f"{original_codes=}")
        print(f"{to_replace_codes=}")
        print(f"{replaced_codes=}")
        # 预处理A：记录非空行（去除空格后非空）及其原始索引
        original_codes_clean = []
        A_original_indices = []
        for i, line in enumerate(original_codes):
            cleaned = line.replace(" ", "")
            if cleaned:  # 非空行
                original_codes_clean.append(cleaned)
                A_original_indices.append(i)

        # 预处理B：过滤空行并去除空格
        B_clean = []
        for line in to_replace_codes:
            cleaned = line.replace(" ", "")
            if cleaned:  # 非空行
                B_clean.append(cleaned)

        # 处理B_clean为空的情况（B中无非空行）
        if not B_clean:
            return 0, replaced_codes + original_codes  # 在A开头插入C

        len_B = len(B_clean)
        len_A_clean = len(original_codes_clean)

        # 在A_clean中搜索B_clean
        found = False
        start_clean_index = -1
        for i in range(len_A_clean - len_B + 1):
            if original_codes_clean[i:i + len_B] == B_clean:
                if found:
                    return 2, None
                found = True
                start_clean_index = i

        if not found:
            return 1, None  # 未找到匹配，返回原始A

        # 计算原始A中的替换范围
        start_index = A_original_indices[start_clean_index]
        end_index = A_original_indices[start_clean_index + len_B - 1]

        # 执行替换
        return 0, original_codes[:start_index] + replaced_codes + original_codes[end_index + 1:]

    async def execute(self, **kwargs) -> ToolResult:
        file_path = kwargs.get("file_path", "")
        original_code_snippet = kwargs.get("original_code_snippet", "")
        replaced_code_snippet = kwargs.get("replaced_code_snippet", "")
        # 检查 original_code_snippet 和 replaced_code_snippet 是否为空
        if original_code_snippet == "":
            return ToolResult(error="original_code_snippet 不能为空")

        # 检查file_path是否为空
        if file_path == "":
            return ToolResult(error="file_path cannot be empty")
        file_path = convert_to_end_abs_path(file_path)

        # 检查file_path是否为文件
        if not os.path.isfile(file_path):
            return ToolResult(error=f"{file_path} 不是一个有效的文件")

        original_code_snippet = original_code_snippet.expandtabs()
        replaced_code_snippet = replaced_code_snippet.expandtabs()

        # 读取文件内容
        try:
            content = read_file(file_path)
        except Exception as e:
            return ToolResult(error=f"读取文件失败: {str(e)}")

        result_sign, new_content_lines = self.replace_code_block(content.splitlines(),
                                                                 original_code_snippet.splitlines(),
                                                                 replaced_code_snippet.splitlines())
        if result_sign == 1:
            return ToolResult(error="未在文件中找到 original_code_snippet")
        elif result_sign == 2:
            return ToolResult(error="original_code_snippet 在文件中出现多次，请提供更多上下文以唯一定位")

        new_content = "\n".join(new_content_lines)

        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(new_content)
        except Exception as e:
            return ToolResult(error=f"写入文件失败: {str(e)}")

        return ToolResult(content="文件编辑成功")


class RemoveFileTool(BaseExecTool):
    name: str = "remove_file"
    description: str = """使用这个工具删除某个文件"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "需要被删除的文件的绝对路径",
            }
        },
        "required": ["file_path"]
    }

    async def execute(self, **kwargs) -> ToolResult:
        file_path = kwargs.get("file_path", "")
        if file_path == "":
            return ToolResult(error="file_path cannot be empty")
        file_path = convert_to_end_abs_path(file_path)
        if not os.path.exists(file_path):
            return ToolResult(error=f"File {file_path} does not exist")
        os.remove(file_path)
        return ToolResult(content="文件删除成功")


if __name__ == '__main__':
    test_file_path = "D:\\Code\\aaa.py"
    test_result = asyncio.run(
        WriteFileTool().execute(file_content="hello world\nimport numpy", file_path=test_file_path))
    print(f"WriteFileTool {test_result}")
    test_content = read_file(file_path=test_file_path)
    print(f"{test_content=}")

    test_result = asyncio.run(EditFileTool().execute(file_path=test_file_path, original_code_snippet="import numpy",
                                                     replaced_code_snippet="import numpy as py"))
    print(f"\nEditFileTool {test_result}")
    test_content = read_file(file_path=test_file_path)
    print(f"{test_content=}")

    test_result = asyncio.run(RemoveFileTool().execute(file_path=test_file_path))
    print(f"\nRemoveFileTool {test_result}")
    print(f"File exist: {os.path.exists(test_file_path)}")
