import json
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional
from backend.src.tools import BaseExecTool, ToolResult
from backend.tests.end2end.config import e2e_settings
from backend.src.core.logging import get_module_logger

logger = get_module_logger(__name__)


class CodebaseInsightTool(BaseExecTool):
    name: str = "codebase_insight"
    description: str = """工具名称：
代码库分析总结工具`codebase_insight`

工具用途：
- 为代理在「陌生代码库」中快速建立全局认知：生成包含目录结构与每个文件的语义摘要（功能、关键点）的“物理树”视图。
- 适合作为对话会话的“起始地基”，用于后续任务中的路径定位、范围筛选、模块间关系把握与优先级判断。

何时应该使用：
- 首次启动代理、接入新仓库或切换仓库时（作为开场动作，建立全局上下文）。
- 用户意图为“了解/总结整个仓库”或“总体结构梳理”：
  - 例：“这个仓库是干啥的？”、“先帮我过一眼整体结构”、“模块/目录分布如何？”、“入口/核心文件在哪？”、“按目录列一下并总结每个文件干嘛的”。
- 在回答跨模块、全局性的需求前做准备：
  - 例：“实现A功能涉及哪些文件/模块？”、“有哪些配置/适配层？”、“有哪些服务端/前端/工具脚本？”。
- 当用户提供 include/exclude 选择“部分子树”做调研时：
  - 例：“只看 src/ 下的结构和摘要”、“排除 test/ 后给我整体树”。

输入参数：
include: array[string], 包含路径模式列表，支持glob通配符。为空时分析全部文件，有值时仅分析匹配的路径。示例: ["src/**/*.ets", "lib/**"]
exclude: array[string], 排除路径模式列表，支持glob通配符。为空时全仓分析，有值时排除匹配的路径。示例: ["test/**", "**/test*.ets"]

输出格式：
字符串，一份“可读性强”的结构视图，便于获取工程结构与每个文件的作用。
"""
    parameters: dict = {
        "type": "object",
        "properties": {
            "include": {
                "type": "array",
                "items": {
                    "type": "string",
                },
                "description": "包含路径模式列表，支持glob通配符。为空时分析全部文件，有值时仅分析匹配的路径。示例: ['src/**/*.ets', 'lib/**']"
            },
            "exclude": {
                "type": "array",
                "items": {
                    "type": "string",
                },
                "description": "排除路径模式列表，支持glob通配符。为空时全仓分析，有值时排除匹配的路径。示例: ['test/**', '**/test.ets']"
            }
        },
        "required": []
    }

    async def execute(self, **kwargs) -> ToolResult:
        """
        执行代码库查询
        
        Args:
            include: 包含路径模式列表，支持glob通配符。为空时分析全部文件，有值时仅分析匹配的路径。示例: ['src/**/*.ets', 'lib/**']
            exclude: 排除路径模式列表，支持glob通配符。为空时全仓分析，有值时排除匹配的路径。示例: ['test/**', '**/test.ets']
            
        Returns:
            ToolResult: 字符串，输出项目级的包含代码库分析总结的文件树
        """
        include = kwargs.get("include", [])
        exclude = kwargs.get("exclude", [])
        base_url = kwargs.get("base_url", f"http://{e2e_settings.CODEBASE_IP}:{e2e_settings.CODEBASE_PORT}")
        
        try:
            # 构造请求
            request_data = {
                "include": include,
                "exclude": exclude
            }
            
            # 发送HTTP请求
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{base_url}/codebase_insight",
                    json=request_data,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        return ToolResult(
                            error=f"Codebase Insight服务请求失败 (状态码: {response.status}): {error_text}"
                        )
                    
                    result_data = await response.json()
                    
            # 处理和格式化结果
            formatted_result = self._format_codebase_results(result_data)
            
            return ToolResult(
                content=formatted_result
            )
            
        except aiohttp.ClientError as e:
            return ToolResult(
                error=f"网络请求失败: {str(e)}"
            )
        except asyncio.TimeoutError:
            return ToolResult(
                error="请求超时，请检查Codebase Insight服务是否正常运行"
            )
        except json.JSONDecodeError as e:
            return ToolResult(
                error=f"响应格式解析失败: {str(e)}"
            )
        except Exception as e:
            return ToolResult(
                error=f"执行查询时发生错误: {str(e)}"
            )
    
    def _format_codebase_results(self, result_data: Dict[str, Any]) -> str:
        """
        格式化代码库查询结果
        
        Args:
            result_data: 从CodebaseQA服务返回的原始数据
            
        Returns:
            str: 格式化后的结果字符串
        """
        tree = result_data.get("tree", "")
        return tree

async def example_usage():
    tool = CodebaseInsightTool()
    result = await tool.execute(include=[], exclude=[])
    print(result.content)

if __name__ == "__main__":
    asyncio.run(example_usage())