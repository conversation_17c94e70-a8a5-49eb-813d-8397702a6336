import argparse
import os
import pathlib
import re
import subprocess
import sys
from concurrent.futures import ThreadPoolExecutor
from enum import Enum

root_dir = str(pathlib.Path(__file__).parent.parent.parent.parent)
sys.path.append(root_dir)
from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
import time

from backend.src.core.config import settings

app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret!'
socketio = SocketIO(app, async_mode='threading')

# 全局变量存储进程、线程池和状态
process = None
thread_pool = ThreadPoolExecutor(max_workers=5)  # 创建一个最大工作线程数为5的线程池


# 用户输入状态
class InputState:
    WAITING_FOR_PROJECT = 0
    WAITING_FOR_QUERY = 1
    READY = 2
    WAITING_FOR_ANSWER = 3


user_state = InputState.WAITING_FOR_PROJECT

test_script = str(pathlib.Path(__file__).parent) + os.path.sep + 'end2end_test.py'


class MsgType(Enum):
    proj = 'proj'  # 项目目录
    query = 'query'  # 用户需求
    feedback = 'feedback'  # 用户反馈
    msg = 'msg'  # 普通消息
    answer = 'answer' # 询问用户的消息
    stop = 'stop'  # 停止
    terminate = 'terminate'  # 终止


def input_msg(msg_type: MsgType, msg=None):
    match msg_type.value:
        case 'proj':
            process.stdin.write("[proj]" + msg + '\n')
            process.stdin.flush()
        case 'query':
            process.stdin.write("[query]" + msg + '\n')
            process.stdin.flush()
        case 'msg':
            process.stdin.write("[msg]" + msg + '\n')
            process.stdin.flush()
        case 'feedback':
            process.stdin.write("[feedback]" + msg + '\n')
            process.stdin.flush()
        case 'answer':
            process.stdin.write("[answer]" + msg + '\n')
            process.stdin.flush()
        case 'stop':
            process.stdin.write("[stop]\n'")
            process.stdin.flush()
        case 'terminate':
            process.stdin.write("[terminate]\n")
            process.stdin.flush()
        case _:
            print(f"啥也不是的消息：{msg_type.value}")


def clean_line(line: str) -> str:
    """去掉整行所有的 ANSI 转义码"""
    ansi = re.compile(r'\x1b\[[0-9;]*m')
    return ansi.sub('', line)


def process_manager(script_path=test_script):
    global process

    while True:
        try:
            # 启动end2end_test子进程
            process = subprocess.Popen(
                ['python', script_path, '--composer_ip', f'{args.composer_ip}', '--composer_port', f'{args.composer_port}'],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                encoding='utf-8',
                errors='replace',
                universal_newlines=True
            )
            # 处理输入输出
            while True:
                # 读取输出
                output = str(process.stdout.readline()).rstrip()
                if output == '' and process.poll() is not None:
                    break
                print(f"output is {output}")
                output = clean_line(output)
                if output and "[web_log]" in output:
                    output = output.replace('\\n', '\n')[output.find("[web_log]") + 9:].rstrip()
                    socketio.emit('output', {'data': output})

                    if "show_msg_to_user_and_wait_for_response" in output:
                        global user_state
                        user_state = InputState.WAITING_FOR_ANSWER

        except Exception as e:
            socketio.emit('output', {'data': f"Error: {str(e)}"})
            time.sleep(1)  # 出错后等待1秒再重试

        finally:
            if process and process.poll() is None:
                process.terminate()
                try:
                    process.wait(timeout=5)
                except:
                    process.kill()


@app.route('/')
def index():
    return render_template('index.html')


@app.route('/send_input', methods=['POST'])
def handle_input():
    global user_state
    data = request.json
    input_data = data.get('input', '')

    if not input_data:
        return jsonify({'error': 'empty_input'}), 400

    if user_state == InputState.WAITING_FOR_PROJECT:
        # 处理项目目录输入
        input_msg(MsgType.proj, input_data)
        # 更新状态为等待需求输入
        user_state = InputState.WAITING_FOR_QUERY
        # 提示用户输入需求
        socketio.emit('output', {'data': '请输入你的需求'})
        return jsonify({'status': 'project_received'})

    elif user_state == InputState.WAITING_FOR_QUERY:
        # 处理需求输入
        input_msg(MsgType.query, input_data)
        # 更新状态为准备就绪
        user_state = InputState.READY
        return jsonify({'status': 'query_received'})

    elif user_state == InputState.WAITING_FOR_ANSWER:
        # 处理问题回复
        input_msg(MsgType.answer, input_data)
        # 更新状态为准备就绪
        user_state = InputState.READY
        return jsonify({'status': 'input_received'})

    else:  # InputState.READY
        # 处理普通消息
        input_msg(MsgType.msg, input_data)
        return jsonify({'status': 'input_received'})


@app.route('/send_feedback', methods=['POST'])
def handle_feedback():
    data = request.json
    input_data = data.get('input', '')
    input_msg(MsgType.feedback, input_data)
    return jsonify({'status': 'feedback_received'})


@app.route('/stop_script', methods=['POST'])
def handle_stop():
    input_msg(MsgType.stop)
    return jsonify({'status': 'script_stopped'})


@app.route('/stop', methods=['POST'])
def handle_terminate():
    input_msg(MsgType.terminate)

    return jsonify({'status': 'termination_signal_sent'})


@socketio.on('connect')
def handle_connect():
    emit('output', {'data': 'Connected to script server'})
    # 启动进程管理线程（如果尚未启动）
    if not hasattr(app, 'process_future'):
        app.process_future = thread_pool.submit(process_manager)

    # 提示用户输入项目目录
    emit('output', {'data': '请输入工程目录'})


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="DevEcoNextClient websocket test")
    parser.add_argument('--composer_ip', type=str, default=settings.COMPOSER_IP, help="Composer server IP address")
    parser.add_argument('--composer_port', type=str, default=settings.COMPOSER_PORT, help="Composer server port")
    args = parser.parse_args()
    socketio.run(app, debug=True, allow_unsafe_werkzeug=True)
