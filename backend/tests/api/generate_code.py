import logging
from unittest import TestCase

from backend.main import generate_code
from backend.src.schemas.session_data_schema import SessionData
from backend.src.session.session import Session

class TestGenerateCodeFactory(TestCase):
    def setUp(self):
        self.session_data = SessionData(device_id="1", session_id="2", interaction_id="3")
        self.session = Session(self.session_data)
        logging.basicConfig(level=logging.INFO)

    async def test_single_tool_task(self):
        msg = {
            "session": {
                "device_id": "123",
                "session_id": "456",
                "interaction_id": "789"
            },
            "data": {
                "msg_type": "query_agent",
                "msg_id": "123",
                "msg": {
                    "query": "请帮我看看代码写得好不好。代码目录在D:/workspace/project"
                }
            },
            "tools": [
                {
                    'type': 'function',
                    'function': {
                        'name': 'list_dir',
                        'description': '查看目录.',
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'directory': {
                                    'type': 'string',
                                    'description': '绝对路径.',
                                },
                            },
                            'required': ['directory'],
                        },
                    }
                }, {
                    'type': 'function',
                    'function': {
                        'name': 'view_file',
                        'description': '查看文件.',
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'file_path': {
                                    'type': 'string',
                                    'description': '文件地址.',
                                },
                            },
                            'required': ['file_path'],
                        },
                    }
                }
            ]
        }
        print(await generate_code(self.session, msg))
